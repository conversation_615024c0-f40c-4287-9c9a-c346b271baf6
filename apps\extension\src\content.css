/* Project Sentinel - Content Script Styles */

.sentinel-loading,
.sentinel-alert {
  position: absolute;
  top: 10px;
  right: 10px;
  z-index: 10000;
  padding: 12px 16px;
  border-radius: 8px;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  font-size: 14px;
  font-weight: 500;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
  backdrop-filter: blur(10px);
  max-width: 300px;
  animation: sentinelFadeIn 0.3s ease-out;
}

.sentinel-loading {
  background: rgba(59, 130, 246, 0.95);
  color: white;
  display: flex;
  align-items: center;
  gap: 8px;
}

.sentinel-spinner {
  width: 16px;
  height: 16px;
  border: 2px solid rgba(255, 255, 255, 0.3);
  border-top: 2px solid white;
  border-radius: 50%;
  animation: sentinelSpin 1s linear infinite;
}

.sentinel-alert.sentinel-danger {
  background: rgba(239, 68, 68, 0.95);
  color: white;
  border: 2px solid #dc2626;
}

.sentinel-alert.sentinel-safe {
  background: rgba(34, 197, 94, 0.95);
  color: white;
  border: 2px solid #16a34a;
}

.sentinel-alert.sentinel-error {
  background: rgba(251, 146, 60, 0.95);
  color: white;
  border: 2px solid #ea580c;
}

.sentinel-alert-header {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 8px;
  font-weight: 600;
}

.sentinel-alert-body {
  font-size: 13px;
  line-height: 1.4;
}

.sentinel-alert-body p {
  margin: 4px 0;
}

.sentinel-warning {
  font-weight: 600;
  margin-top: 8px !important;
  padding-top: 8px;
  border-top: 1px solid rgba(255, 255, 255, 0.3);
}

.sentinel-icon {
  font-size: 16px;
  display: inline-block;
}

/* Animations */
@keyframes sentinelFadeIn {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes sentinelSpin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

/* Platform-specific adjustments */
[data-testid="videoComponent"] .sentinel-loading,
[data-testid="videoComponent"] .sentinel-alert {
  /* Twitter/X specific positioning */
  top: 8px;
  right: 8px;
}

.ytd-player .sentinel-loading,
.ytd-player .sentinel-alert {
  /* YouTube specific positioning */
  top: 16px;
  right: 16px;
}

/* Responsive design */
@media (max-width: 768px) {
  .sentinel-loading,
  .sentinel-alert {
    font-size: 12px;
    padding: 8px 12px;
    max-width: 250px;
  }
  
  .sentinel-alert-header {
    font-size: 13px;
  }
}

/* High contrast mode support */
@media (prefers-contrast: high) {
  .sentinel-alert.sentinel-danger {
    background: #dc2626;
    border-color: #991b1b;
  }
  
  .sentinel-alert.sentinel-safe {
    background: #16a34a;
    border-color: #15803d;
  }
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
  .sentinel-loading,
  .sentinel-alert {
    animation: none;
  }
  
  .sentinel-spinner {
    animation: none;
    border-top-color: transparent;
  }
}
