"""
GenConViT Variational Autoencoder (VAE) Network
Simplified implementation for Project Sentinel
"""

import torch
import torch.nn as nn
import timm
from timm.models.layers import trunc_normal_

class GenConViTVAE(nn.Module):
    """
    GenConViT Variational Autoencoder Network
    Uses ConvNeXt backbone with Swin Transformer embedder and VAE latent space
    """
    
    def __init__(self, config):
        super(GenConViTVAE, self).__init__()
        self.config = config
        
        # ConvNeXt backbone for feature extraction
        self.backbone = timm.create_model(
            config["model"]["backbone"], 
            pretrained=True,
            num_classes=0,  # Remove classification head
            global_pool=""  # Remove global pooling
        )
        
        # Swin Transformer embedder
        self.embedder = timm.create_model(
            config["model"]["embedder"],
            pretrained=True,
            num_classes=0,
            global_pool=""
        )
        
        # Get feature dimensions
        self.backbone_features = self._get_backbone_features()
        self.embedder_features = self._get_embedder_features()
        self.total_features = self.backbone_features + self.embedder_features
        
        # VAE Encoder
        self.encoder = nn.Sequential(
            nn.Linear(self.total_features, 1024),
            nn.ReLU(),
            nn.Dropout(0.3),
            nn.Linear(1024, 512),
            nn.ReLU(),
            nn.Dropout(0.3)
        )
        
        # VAE latent space parameters
        latent_dims = config["model"]["latent_dims"]
        self.fc_mu = nn.Linear(512, latent_dims)
        self.fc_logvar = nn.Linear(512, latent_dims)
        
        # VAE Decoder
        self.decoder = nn.Sequential(
            nn.Linear(latent_dims, 512),
            nn.ReLU(),
            nn.Dropout(0.3),
            nn.Linear(512, 1024),
            nn.ReLU(),
            nn.Dropout(0.3),
            nn.Linear(1024, self.total_features)
        )
        
        # Classification head
        self.classifier = nn.Sequential(
            nn.Linear(latent_dims, 256),
            nn.ReLU(),
            nn.Dropout(0.5),
            nn.Linear(256, config["num_classes"])
        )
        
        self._init_weights()
    
    def _get_backbone_features(self):
        """Get backbone feature dimensions"""
        # Simplified: use known dimensions for tiny models
        return 768 if "tiny" in self.config["model"]["backbone"] else 1024

    def _get_embedder_features(self):
        """Get embedder feature dimensions"""
        # Simplified: use known dimensions for tiny models
        return 768 if "tiny" in self.config["model"]["embedder"] else 1024
    
    def _init_weights(self):
        """Initialize weights"""
        for m in self.modules():
            if isinstance(m, nn.Linear):
                trunc_normal_(m.weight, std=0.02)
                if m.bias is not None:
                    nn.init.constant_(m.bias, 0)
    
    def reparameterize(self, mu, logvar):
        """VAE reparameterization trick"""
        std = torch.exp(0.5 * logvar)
        eps = torch.randn_like(std)
        return mu + eps * std
    
    def forward(self, x):
        """Forward pass"""
        batch_size = x.size(0)
        
        # Extract features from both networks
        backbone_features = self.backbone(x)
        embedder_features = self.embedder(x)
        
        # Flatten features
        backbone_features = backbone_features.view(batch_size, -1)
        embedder_features = embedder_features.view(batch_size, -1)
        
        # Concatenate features
        combined_features = torch.cat([backbone_features, embedder_features], dim=1)
        
        # Encode
        encoded = self.encoder(combined_features)
        
        # VAE latent parameters
        mu = self.fc_mu(encoded)
        logvar = self.fc_logvar(encoded)
        
        # Sample from latent space
        z = self.reparameterize(mu, logvar)
        
        # Decode (for reconstruction loss during training)
        reconstructed = self.decoder(z)
        
        # Classify
        classification = self.classifier(z)
        
        return classification, (mu, logvar, reconstructed)
