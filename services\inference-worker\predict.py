"""
Replicate prediction interface for Project Sentinel
"""

import os
import tempfile
import asyncio
from typing import Any
from cog import BasePredictor, Input, Path

from genconvit_inference import GenConViTInference

class Predictor(BasePredictor):
    """Replicate predictor for GenConViT deepfake detection"""
    
    def setup(self) -> None:
        """Load the model into memory to make running multiple predictions efficient"""
        self.model = GenConViTInference()
        # Run async setup in sync context
        asyncio.run(self.model.load_model())
    
    def predict(
        self,
        video: Path = Input(description="Video file to analyze for deepfakes"),
        num_frames: int = Input(
            description="Number of frames to analyze", 
            default=15, 
            ge=5, 
            le=30
        ),
    ) -> dict[str, Any]:
        """Run deepfake detection on a video"""
        
        # Convert Path to string
        video_path = str(video)
        
        # Run prediction
        result = asyncio.run(self.model.predict_video(video_path, num_frames))
        
        return {
            "deepfake_probability": result["deepfake_probability"],
            "confidence_score": result["confidence_score"],
            "processing_time": result["processing_time"],
            "frames_analyzed": result["frames_analyzed"],
            "is_deepfake": result["deepfake_probability"] > 0.5,
            "risk_level": self._get_risk_level(result["deepfake_probability"]),
            "device_used": result["device_used"]
        }
    
    def _get_risk_level(self, probability: float) -> str:
        """Determine risk level based on deepfake probability"""
        if probability >= 0.8:
            return "HIGH"
        elif probability >= 0.6:
            return "MEDIUM"
        elif probability >= 0.4:
            return "LOW"
        else:
            return "MINIMAL"
