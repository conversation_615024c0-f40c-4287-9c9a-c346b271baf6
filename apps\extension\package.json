{"name": "sentinel-extension", "version": "1.0.0", "description": "Project Sentinel - Real-time Deepfake Detection Browser Extension", "type": "module", "scripts": {"dev": "vite", "build": "tsc && vite build", "preview": "vite preview", "type-check": "tsc --noEmit", "lint": "eslint src --ext ts,tsx --report-unused-disable-directives --max-warnings 0"}, "dependencies": {"webextension-polyfill": "^0.10.0"}, "devDependencies": {"@types/chrome": "^0.0.246", "@types/webextension-polyfill": "^0.10.0", "@typescript-eslint/eslint-plugin": "^6.0.0", "@typescript-eslint/parser": "^6.0.0", "eslint": "^8.45.0", "typescript": "^5.0.2", "vite": "^4.4.5", "vite-plugin-web-extension": "^4.0.0"}, "keywords": ["browser-extension", "deepfake", "detection", "security", "chrome"], "author": "Project Sentinel Team", "license": "MIT"}