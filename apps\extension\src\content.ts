/**
 * Project Sentinel - Content Script
 * Detects videos and manages deepfake analysis alerts
 */

interface AnalysisResult {
  job_id: string;
  status: string;
  deepfake_probability?: number;
  confidence_score?: number;
  error?: string;
}

class SentinelVideoDetector {
  private apiBaseUrl = 'https://sentinel-api.vercel.app'; // Replace with your API URL
  private processedVideos = new Set<string>();
  private activeJobs = new Map<string, string>(); // video URL -> job_id
  private observer: MutationObserver;

  constructor() {
    this.observer = new MutationObserver(this.handleMutations.bind(this));
    this.init();
  }

  private init(): void {
    console.log('🛡️ Project Sentinel initialized');
    
    // Start observing DOM changes
    this.observer.observe(document.body, {
      childList: true,
      subtree: true
    });

    // Scan existing videos
    this.scanForVideos();
  }

  private handleMutations(mutations: MutationRecord[]): void {
    for (const mutation of mutations) {
      if (mutation.type === 'childList') {
        mutation.addedNodes.forEach(node => {
          if (node.nodeType === Node.ELEMENT_NODE) {
            const element = node as Element;
            this.scanElement(element);
          }
        });
      }
    }
  }

  private scanForVideos(): void {
    const videos = document.querySelectorAll('video');
    videos.forEach(video => this.processVideo(video));
  }

  private scanElement(element: Element): void {
    // Check if element is a video
    if (element.tagName === 'VIDEO') {
      this.processVideo(element as HTMLVideoElement);
    }
    
    // Check for videos in children
    const videos = element.querySelectorAll('video');
    videos.forEach(video => this.processVideo(video));
  }

  private async processVideo(video: HTMLVideoElement): Promise<void> {
    try {
      const videoUrl = this.getVideoUrl(video);
      
      if (!videoUrl || this.processedVideos.has(videoUrl)) {
        return;
      }

      console.log('🎬 Found new video:', videoUrl);
      this.processedVideos.add(videoUrl);

      // Add loading indicator
      this.addLoadingIndicator(video);

      // Submit for analysis
      const jobId = await this.submitForAnalysis(videoUrl);
      this.activeJobs.set(videoUrl, jobId);

      // Poll for results
      this.pollForResults(video, videoUrl, jobId);

    } catch (error) {
      console.error('❌ Error processing video:', error);
    }
  }

  private getVideoUrl(video: HTMLVideoElement): string | null {
    // Try different methods to get video URL
    if (video.src) {
      return video.src;
    }
    
    // Check source elements
    const sources = video.querySelectorAll('source');
    for (const source of sources) {
      if (source.src) {
        return source.src;
      }
    }
    
    // Platform-specific URL extraction
    if (window.location.hostname.includes('youtube.com')) {
      return this.getYouTubeVideoUrl();
    }
    
    if (window.location.hostname.includes('twitter.com') || window.location.hostname.includes('x.com')) {
      return this.getTwitterVideoUrl(video);
    }
    
    return null;
  }

  private getYouTubeVideoUrl(): string | null {
    const urlParams = new URLSearchParams(window.location.search);
    const videoId = urlParams.get('v');
    return videoId ? `https://www.youtube.com/watch?v=${videoId}` : null;
  }

  private getTwitterVideoUrl(video: HTMLVideoElement): string | null {
    // Twitter videos are often in blob format, try to find the original URL
    const videoContainer = video.closest('[data-testid="videoComponent"]');
    if (videoContainer) {
      // This is a simplified approach - in production, you'd need more sophisticated URL extraction
      return video.src || video.currentSrc;
    }
    return null;
  }

  private async submitForAnalysis(videoUrl: string): Promise<string> {
    const response = await fetch(`${this.apiBaseUrl}/api/analyze`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        video_url: videoUrl,
        num_frames: 15
      })
    });

    if (!response.ok) {
      throw new Error(`Analysis submission failed: ${response.statusText}`);
    }

    const result = await response.json();
    return result.job_id;
  }

  private async pollForResults(video: HTMLVideoElement, videoUrl: string, jobId: string): Promise<void> {
    const maxAttempts = 30; // 5 minutes max
    let attempts = 0;

    const poll = async (): Promise<void> => {
      try {
        attempts++;
        
        const response = await fetch(`${this.apiBaseUrl}/api/status/${jobId}`);
        if (!response.ok) {
          throw new Error(`Status check failed: ${response.statusText}`);
        }

        const result: AnalysisResult = await response.json();

        if (result.status === 'completed') {
          this.removeLoadingIndicator(video);
          this.showResult(video, result);
          this.activeJobs.delete(videoUrl);
        } else if (result.status === 'failed') {
          this.removeLoadingIndicator(video);
          this.showError(video, result.error || 'Analysis failed');
          this.activeJobs.delete(videoUrl);
        } else if (attempts < maxAttempts) {
          // Continue polling
          setTimeout(poll, 10000); // Poll every 10 seconds
        } else {
          // Timeout
          this.removeLoadingIndicator(video);
          this.showError(video, 'Analysis timeout');
          this.activeJobs.delete(videoUrl);
        }

      } catch (error) {
        console.error('❌ Polling error:', error);
        this.removeLoadingIndicator(video);
        this.showError(video, 'Connection error');
        this.activeJobs.delete(videoUrl);
      }
    };

    // Start polling after 5 seconds
    setTimeout(poll, 5000);
  }

  private addLoadingIndicator(video: HTMLVideoElement): void {
    const indicator = document.createElement('div');
    indicator.className = 'sentinel-loading';
    indicator.innerHTML = `
      <div class="sentinel-spinner"></div>
      <span>🛡️ Sentinel Analyzing...</span>
    `;

    this.addOverlay(video, indicator);
  }

  private removeLoadingIndicator(video: HTMLVideoElement): void {
    const existing = video.parentElement?.querySelector('.sentinel-loading');
    if (existing) {
      existing.remove();
    }
  }

  private showResult(video: HTMLVideoElement, result: AnalysisResult): void {
    const probability = result.deepfake_probability || 0;
    const isDeepfake = probability > 0.6; // Threshold for alert

    if (isDeepfake) {
      const alert = document.createElement('div');
      alert.className = 'sentinel-alert sentinel-danger';
      alert.innerHTML = `
        <div class="sentinel-alert-header">
          <span class="sentinel-icon">⚠️</span>
          <strong>SENTINEL ALERT: POTENTIAL DEEPFAKE</strong>
        </div>
        <div class="sentinel-alert-body">
          <p>Deepfake Probability: <strong>${(probability * 100).toFixed(1)}%</strong></p>
          <p>Confidence: ${((result.confidence_score || 0) * 100).toFixed(1)}%</p>
          <p class="sentinel-warning">⚠️ This video may contain manipulated content. Exercise caution before making investment decisions.</p>
        </div>
      `;

      this.addOverlay(video, alert);

      // Log detection
      console.log('🚨 DEEPFAKE DETECTED:', {
        probability,
        confidence: result.confidence_score,
        jobId: result.job_id
      });
    } else {
      // Show brief "verified" indicator
      const verified = document.createElement('div');
      verified.className = 'sentinel-alert sentinel-safe';
      verified.innerHTML = `
        <span class="sentinel-icon">✅</span>
        <span>Sentinel Verified</span>
      `;

      this.addOverlay(video, verified);

      // Auto-remove after 3 seconds
      setTimeout(() => verified.remove(), 3000);
    }
  }

  private showError(video: HTMLVideoElement, error: string): void {
    const errorDiv = document.createElement('div');
    errorDiv.className = 'sentinel-alert sentinel-error';
    errorDiv.innerHTML = `
      <span class="sentinel-icon">❌</span>
      <span>Sentinel Analysis Failed: ${error}</span>
    `;

    this.addOverlay(video, errorDiv);

    // Auto-remove after 5 seconds
    setTimeout(() => errorDiv.remove(), 5000);
  }

  private addOverlay(video: HTMLVideoElement, overlay: HTMLElement): void {
    // Ensure video container is positioned relatively
    const container = video.parentElement;
    if (container) {
      const computedStyle = window.getComputedStyle(container);
      if (computedStyle.position === 'static') {
        container.style.position = 'relative';
      }

      // Position overlay absolutely within container
      overlay.style.position = 'absolute';
      overlay.style.zIndex = '10000';

      container.appendChild(overlay);
    }
  }

  private logDetection(result: AnalysisResult): void {
    // Send detection event to background script for analytics
    chrome.runtime.sendMessage({
      type: 'DETECTION_EVENT',
      data: {
        deepfake_probability: result.deepfake_probability,
        confidence_score: result.confidence_score,
        timestamp: Date.now(),
        url: window.location.href
      }
    });
  }
}

// Initialize the detector when DOM is ready
if (document.readyState === 'loading') {
  document.addEventListener('DOMContentLoaded', () => new SentinelVideoDetector());
} else {
  new SentinelVideoDetector();
}

// Export for testing
(window as any).SentinelVideoDetector = SentinelVideoDetector;
