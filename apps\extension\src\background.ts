/**
 * Project Sentinel - Background Service Worker
 * Manages extension lifecycle and API communications
 */

interface JobStatus {
  job_id: string;
  status: string;
  deepfake_probability?: number;
  confidence_score?: number;
  error?: string;
}

class SentinelBackground {
  private apiBaseUrl = 'https://sentinel-api.vercel.app'; // Replace with your API URL

  constructor() {
    this.init();
  }

  private init(): void {
    console.log('🛡️ Project Sentinel Background Service initialized');
    
    // Handle extension installation
    chrome.runtime.onInstalled.addListener(this.handleInstall.bind(this));
    
    // Handle messages from content scripts
    chrome.runtime.onMessage.addListener(this.handleMessage.bind(this));
    
    // Handle tab updates to inject content scripts
    chrome.tabs.onUpdated.addListener(this.handleTabUpdate.bind(this));
  }

  private handleInstall(details: chrome.runtime.InstalledDetails): void {
    if (details.reason === 'install') {
      console.log('🎉 Project Sentinel installed successfully');
      
      // Set default settings
      chrome.storage.sync.set({
        enabled: true,
        alertThreshold: 0.6,
        autoScan: true
      });
      
      // Open welcome page
      chrome.tabs.create({
        url: chrome.runtime.getURL('popup.html')
      });
    }
  }

  private handleMessage(
    message: any,
    sender: chrome.runtime.MessageSender,
    sendResponse: (response?: any) => void
  ): boolean {
    switch (message.type) {
      case 'ANALYZE_VIDEO':
        this.analyzeVideo(message.videoUrl, message.tabId)
          .then(sendResponse)
          .catch(error => sendResponse({ error: error.message }));
        return true; // Indicates async response
      
      case 'GET_JOB_STATUS':
        this.getJobStatus(message.jobId)
          .then(sendResponse)
          .catch(error => sendResponse({ error: error.message }));
        return true;
      
      case 'GET_SETTINGS':
        this.getSettings()
          .then(sendResponse)
          .catch(error => sendResponse({ error: error.message }));
        return true;
      
      case 'UPDATE_SETTINGS':
        this.updateSettings(message.settings)
          .then(sendResponse)
          .catch(error => sendResponse({ error: error.message }));
        return true;
    }
    
    return false;
  }

  private handleTabUpdate(
    tabId: number,
    changeInfo: chrome.tabs.TabChangeInfo,
    tab: chrome.tabs.Tab
  ): void {
    // Inject content script when page is loaded on supported sites
    if (changeInfo.status === 'complete' && tab.url) {
      const supportedSites = [
        'twitter.com', 'x.com', 'youtube.com', 'linkedin.com', 'web.telegram.org'
      ];
      
      const isSupported = supportedSites.some(site => tab.url!.includes(site));
      
      if (isSupported) {
        chrome.scripting.executeScript({
          target: { tabId },
          files: ['content.js']
        }).catch(error => {
          console.log('Content script already injected or failed:', error);
        });
      }
    }
  }

  private async analyzeVideo(videoUrl: string, tabId?: number): Promise<{ job_id: string }> {
    try {
      const response = await fetch(`${this.apiBaseUrl}/api/analyze`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          video_url: videoUrl,
          num_frames: 15
        })
      });

      if (!response.ok) {
        throw new Error(`Analysis failed: ${response.statusText}`);
      }

      const result = await response.json();
      
      // Store job info
      chrome.storage.local.set({
        [`job_${result.job_id}`]: {
          videoUrl,
          tabId,
          submittedAt: Date.now()
        }
      });

      return result;

    } catch (error) {
      console.error('❌ Video analysis submission failed:', error);
      throw error;
    }
  }

  private async getJobStatus(jobId: string): Promise<JobStatus> {
    try {
      const response = await fetch(`${this.apiBaseUrl}/api/status/${jobId}`);
      
      if (!response.ok) {
        throw new Error(`Status check failed: ${response.statusText}`);
      }

      return await response.json();

    } catch (error) {
      console.error('❌ Job status check failed:', error);
      throw error;
    }
  }

  private async getSettings(): Promise<any> {
    return new Promise((resolve) => {
      chrome.storage.sync.get({
        enabled: true,
        alertThreshold: 0.6,
        autoScan: true
      }, resolve);
    });
  }

  private async updateSettings(settings: any): Promise<void> {
    return new Promise((resolve) => {
      chrome.storage.sync.set(settings, resolve);
    });
  }
}

// Initialize background service
new SentinelBackground();
