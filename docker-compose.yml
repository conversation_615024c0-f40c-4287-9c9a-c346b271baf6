# Project Sentinel - Development Environment
version: '3.8'

services:
  # AI Inference Service
  inference-worker:
    build:
      context: ./services/inference-worker
      dockerfile: Dockerfile
    ports:
      - "8000:8000"
    environment:
      - PYTHONPATH=/app
      - TORCH_HOME=/app/.torch
    volumes:
      - ./services/inference-worker/weight:/app/weight
      - inference_cache:/app/.torch
    deploy:
      resources:
        reservations:
          devices:
            - driver: nvidia
              count: 1
              capabilities: [gpu]
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 60s

  # Backend API (for local development)
  api:
    build:
      context: ./services/api
      dockerfile: Dockerfile.dev
    ports:
      - "3000:3000"
    environment:
      - INFERENCE_SERVICE_URL=http://inference-worker:8000
      - NODE_ENV=development
    depends_on:
      - inference-worker
      - redis
    volumes:
      - ./services/api:/app
      - /app/node_modules

  # Redis for job caching (development)
  redis:
    image: redis:7-alpine
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data

  # Nginx reverse proxy (optional)
  nginx:
    image: nginx:alpine
    ports:
      - "80:80"
    volumes:
      - ./deploy/nginx.conf:/etc/nginx/nginx.conf
    depends_on:
      - api
      - inference-worker

volumes:
  inference_cache:
  redis_data:
