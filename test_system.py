"""
Project Sentinel - System Integration Test
Tests the complete pipeline from video input to deepfake detection
"""

import asyncio
import json
import time
import requests
from typing import Dict, Any

class SentinelSystemTest:
    """Complete system test for Project Sentinel"""
    
    def __init__(self):
        self.api_base_url = "http://localhost:3000"  # Local API
        self.inference_url = "http://localhost:8000"  # Local inference service
        
    async def run_complete_test(self):
        """Run complete system integration test"""
        print("🛡️ Project Sentinel - System Integration Test")
        print("=" * 50)
        
        # Test 1: Health checks
        await self.test_health_checks()
        
        # Test 2: Direct inference service
        await self.test_inference_service()
        
        # Test 3: Full API pipeline
        await self.test_api_pipeline()
        
        print("\n🎯 System integration test completed!")
    
    async def test_health_checks(self):
        """Test health endpoints"""
        print("\n1️⃣ Testing Health Endpoints...")
        
        # Test inference service health
        try:
            response = requests.get(f"{self.inference_url}/health", timeout=10)
            if response.status_code == 200:
                health_data = response.json()
                print(f"✅ Inference Service: {health_data['status']}")
                print(f"   Device: {health_data['device']}")
                print(f"   Model Loaded: {health_data['model_loaded']}")
            else:
                print(f"❌ Inference Service: HTTP {response.status_code}")
        except Exception as e:
            print(f"❌ Inference Service: {e}")
        
        # Test API health
        try:
            response = requests.get(f"{self.api_base_url}/health", timeout=10)
            if response.status_code == 200:
                health_data = response.json()
                print(f"✅ API Service: {health_data['status']}")
                print(f"   Cache Size: {health_data['cache_size']}")
            else:
                print(f"❌ API Service: HTTP {response.status_code}")
        except Exception as e:
            print(f"❌ API Service: {e}")
    
    async def test_inference_service(self):
        """Test direct inference service"""
        print("\n2️⃣ Testing Direct Inference Service...")
        
        # Use a sample video URL (replace with actual test video)
        test_video_url = "https://sample-videos.com/zip/10/mp4/SampleVideo_1280x720_1mb.mp4"
        
        test_payload = {
            "video_url": test_video_url,
            "job_id": "test-direct-123",
            "num_frames": 10
        }
        
        try:
            print(f"📤 Submitting test video: {test_video_url}")
            response = requests.post(
                f"{self.inference_url}/predict-sync",
                json=test_payload,
                timeout=120  # Allow time for processing
            )
            
            if response.status_code == 200:
                result = response.json()
                print("✅ Direct inference successful!")
                print(f"   Deepfake Probability: {result.get('deepfake_probability', 'N/A'):.3f}")
                print(f"   Confidence Score: {result.get('confidence_score', 'N/A'):.3f}")
                print(f"   Processing Time: {result.get('processing_time', 'N/A'):.2f}s")
                print(f"   Frames Analyzed: {result.get('frames_analyzed', 'N/A')}")
            else:
                print(f"❌ Direct inference failed: HTTP {response.status_code}")
                print(f"   Response: {response.text}")
                
        except Exception as e:
            print(f"❌ Direct inference error: {e}")
    
    async def test_api_pipeline(self):
        """Test complete API pipeline"""
        print("\n3️⃣ Testing Complete API Pipeline...")
        
        test_video_url = "https://sample-videos.com/zip/10/mp4/SampleVideo_1280x720_1mb.mp4"
        
        # Step 1: Submit analysis
        try:
            print("📤 Submitting analysis request...")
            response = requests.post(
                f"{self.api_base_url}/api/analyze",
                json={
                    "video_url": test_video_url,
                    "num_frames": 10
                },
                timeout=10
            )
            
            if response.status_code != 200:
                print(f"❌ Analysis submission failed: HTTP {response.status_code}")
                return
            
            result = response.json()
            job_id = result["job_id"]
            print(f"✅ Analysis submitted! Job ID: {job_id}")
            
        except Exception as e:
            print(f"❌ Analysis submission error: {e}")
            return
        
        # Step 2: Poll for results
        print("🔄 Polling for results...")
        max_attempts = 30  # 5 minutes max
        attempt = 0
        
        while attempt < max_attempts:
            try:
                response = requests.get(f"{self.api_base_url}/api/status/{job_id}")
                
                if response.status_code == 200:
                    status = response.json()
                    print(f"   Status: {status['status']}")
                    
                    if status['status'] == 'completed':
                        print("✅ Analysis completed!")
                        print(f"   Deepfake Probability: {status.get('deepfake_probability', 'N/A'):.3f}")
                        print(f"   Confidence Score: {status.get('confidence_score', 'N/A'):.3f}")
                        print(f"   Processing Time: {status.get('processing_time', 'N/A'):.2f}s")
                        break
                    elif status['status'] == 'failed':
                        print(f"❌ Analysis failed: {status.get('error', 'Unknown error')}")
                        break
                    else:
                        # Still processing
                        attempt += 1
                        await asyncio.sleep(10)
                else:
                    print(f"❌ Status check failed: HTTP {response.status_code}")
                    break
                    
            except Exception as e:
                print(f"❌ Status check error: {e}")
                break
        
        if attempt >= max_attempts:
            print("⏰ Analysis timeout - this may be normal for large videos")

def main():
    """Run system test"""
    test = SentinelSystemTest()
    asyncio.run(test.run_complete_test())

if __name__ == "__main__":
    main()
