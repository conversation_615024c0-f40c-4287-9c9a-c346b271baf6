# Project Sentinel - Environment Variables Template
# Copy this file to .env and fill in your values

# =============================================================================
# AI Inference Service Configuration
# =============================================================================

# PyTorch settings
TORCH_HOME=/app/.torch
PYTHONPATH=/app

# Model settings
MODEL_DEVICE=cuda  # or 'cpu' for CPU-only deployment
ENABLE_FP16=true   # Enable half-precision for GPU efficiency

# =============================================================================
# Backend API Configuration  
# =============================================================================

# Inference service URL (update after deploying to Replicate)
INFERENCE_SERVICE_URL=http://localhost:8000

# Replicate API (if using Replicate for inference)
REPLICATE_API_TOKEN=your_replicate_api_token_here

# Vercel KV Store (for job caching in production)
KV_REST_API_URL=https://your-kv-store.vercel-storage.com
KV_REST_API_TOKEN=your_kv_token_here

# =============================================================================
# Browser Extension Configuration
# =============================================================================

# API base URL (update after deploying to Vercel)
API_BASE_URL=http://localhost:3000

# Extension settings
DEFAULT_ALERT_THRESHOLD=0.6
DEFAULT_NUM_FRAMES=15
ENABLE_AUTO_SCAN=true

# =============================================================================
# Development Configuration
# =============================================================================

# Logging level
LOG_LEVEL=INFO

# Development mode
NODE_ENV=development

# Redis (for local development)
REDIS_URL=redis://localhost:6379

# =============================================================================
# Production Configuration
# =============================================================================

# Domain settings (for production)
PRODUCTION_API_URL=https://sentinel-api.vercel.app
PRODUCTION_INFERENCE_URL=https://api.replicate.com/v1/predictions

# Security settings
CORS_ORIGINS=["https://twitter.com", "https://youtube.com", "https://linkedin.com"]
RATE_LIMIT_PER_MINUTE=10

# Monitoring
SENTRY_DSN=your_sentry_dsn_here
ANALYTICS_ID=your_analytics_id_here
