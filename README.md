# Project Sentinel 🛡️

**Elite AI-Powered Deepfake Detection Platform for Securities Market Protection**

Project Sentinel is a cutting-edge fraud prevention platform that combines state-of-the-art academic research (GenConViT) with production-ready system architecture to protect retail investors from deepfake manipulation in financial markets.

## 🎯 Mission

Detect and prevent deepfake videos of corporate leaders used to manipulate stock prices and deceive retail investors on social media platforms.

## 🏗️ Architecture

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│  Browser Ext    │    │   Backend API   │    │ AI Inference    │
│  (TypeScript)   │◄──►│   (FastAPI)     │◄──►│  (GenConViT)    │
│                 │    │                 │    │                 │
│ • Video Detection│    │ • Job Management│    │ • Face Extract  │
│ • Alert Overlay │    │ • Async Process │    │ • Model Predict │
│ • Real-time UI  │    │ • Result Cache  │    │ • GPU Optimized │
└─────────────────┘    └─────────────────┘    └─────────────────┘
```

## 🚀 Quick Start

### Prerequisites
- Docker & Docker Compose
- Node.js 18+
- Python 3.9+

### 1. <PERSON><PERSON> and Setup
```bash
git clone <repository>
cd HACKTHON
```

### 2. Start AI Inference Service
```bash
cd services/inference-worker
docker build -t sentinel-inference .
docker run -p 8000:8000 sentinel-inference
```

### 3. Start Backend API
```bash
cd services/api
npm install
npm run dev
```

### 4. Install Browser Extension
```bash
cd apps/extension
npm install
npm run build
# Load dist/ folder in Chrome Developer Mode
```

## 📁 Project Structure

```
HACKTHON/
├── services/
│   ├── inference-worker/     # GenConViT AI Service
│   └── api/                  # FastAPI Orchestrator
├── apps/
│   └── extension/            # Browser Extension
├── docs/                     # Documentation
└── deploy/                   # Deployment Configs
```

## 🔬 Technology Stack

- **AI Model**: GenConViT (ConvNeXt + Swin Transformer + Autoencoder/VAE)
- **Backend**: FastAPI + Vercel Serverless
- **Frontend**: TypeScript + Vite + Chrome Extension API
- **Deployment**: Docker + Replicate + Vercel
- **Storage**: Vercel KV Store

## 📊 Performance

- **Accuracy**: 95.8% average across datasets
- **AUC**: 99.3% 
- **Processing Time**: ~10-30 seconds per video
- **Cost**: ~$0.01-0.05 per analysis

## 🏆 Hackathon Advantages

1. **Academic Excellence**: Based on peer-reviewed research (arXiv:2307.07036v2)
2. **Production Ready**: Scalable, serverless architecture
3. **Real-time Protection**: Browser extension with instant alerts
4. **Cost Effective**: Pay-per-use GPU inference
5. **Comprehensive**: Handles multiple deepfake generation methods

---

**Built for the Securities Market Hackathon**  
*Protecting investors through advanced AI technology*
