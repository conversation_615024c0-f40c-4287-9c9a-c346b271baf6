/**
 * Project Sentinel - Popup Interface
 * Extension popup for settings and statistics
 */

interface ExtensionStats {
  videosScanned: number;
  deepfakesDetected: number;
  threatsBlocked: number;
}

interface ExtensionSettings {
  enabled: boolean;
  alertThreshold: number;
  autoScan: boolean;
}

class SentinelPopup {
  private stats: ExtensionStats = {
    videosScanned: 0,
    deepfakesDetected: 0,
    threatsBlocked: 0
  };

  private settings: ExtensionSettings = {
    enabled: true,
    alertThreshold: 0.6,
    autoScan: true
  };

  constructor() {
    this.init();
  }

  private async init(): Promise<void> {
    await this.loadSettings();
    await this.loadStats();
    this.setupEventListeners();
    this.updateUI();
  }

  private async loadSettings(): Promise<void> {
    return new Promise((resolve) => {
      chrome.storage.sync.get({
        enabled: true,
        alertThreshold: 0.6,
        autoScan: true
      }, (result) => {
        this.settings = result as ExtensionSettings;
        resolve();
      });
    });
  }

  private async loadStats(): Promise<void> {
    return new Promise((resolve) => {
      chrome.storage.local.get({
        videosScanned: 0,
        deepfakesDetected: 0,
        threatsBlocked: 0
      }, (result) => {
        this.stats = result as ExtensionStats;
        resolve();
      });
    });
  }

  private setupEventListeners(): void {
    // Enable/disable toggle
    const enabledToggle = document.getElementById('enabledToggle') as HTMLInputElement;
    enabledToggle.addEventListener('change', (e) => {
      this.settings.enabled = (e.target as HTMLInputElement).checked;
      this.saveSettings();
      this.updateStatusIndicator();
    });

    // Auto-scan toggle
    const autoScanToggle = document.getElementById('autoScanToggle') as HTMLInputElement;
    autoScanToggle.addEventListener('change', (e) => {
      this.settings.autoScan = (e.target as HTMLInputElement).checked;
      this.saveSettings();
    });

    // Threshold slider
    const thresholdSlider = document.getElementById('thresholdSlider') as HTMLInputElement;
    thresholdSlider.addEventListener('input', (e) => {
      const value = parseFloat((e.target as HTMLInputElement).value);
      this.settings.alertThreshold = value;
      this.updateThresholdDisplay(value);
      this.saveSettings();
    });

    // Help and settings links
    document.getElementById('helpLink')?.addEventListener('click', (e) => {
      e.preventDefault();
      chrome.tabs.create({ url: 'https://github.com/your-repo/sentinel#readme' });
    });

    document.getElementById('settingsLink')?.addEventListener('click', (e) => {
      e.preventDefault();
      chrome.runtime.openOptionsPage();
    });
  }

  private updateUI(): void {
    // Update stats
    const videosScannedEl = document.getElementById('videosScanned');
    const deepfakesDetectedEl = document.getElementById('deepfakesDetected');
    const threatsBlockedEl = document.getElementById('threatsBlocked');

    if (videosScannedEl) videosScannedEl.textContent = this.stats.videosScanned.toString();
    if (deepfakesDetectedEl) deepfakesDetectedEl.textContent = this.stats.deepfakesDetected.toString();
    if (threatsBlockedEl) threatsBlockedEl.textContent = this.stats.threatsBlocked.toString();

    // Update controls
    const enabledToggle = document.getElementById('enabledToggle') as HTMLInputElement;
    const autoScanToggle = document.getElementById('autoScanToggle') as HTMLInputElement;
    const thresholdSlider = document.getElementById('thresholdSlider') as HTMLInputElement;

    if (enabledToggle) enabledToggle.checked = this.settings.enabled;
    if (autoScanToggle) autoScanToggle.checked = this.settings.autoScan;
    if (thresholdSlider) {
      thresholdSlider.value = this.settings.alertThreshold.toString();
      this.updateThresholdDisplay(this.settings.alertThreshold);
    }

    this.updateStatusIndicator();
  }

  private updateStatusIndicator(): void {
    const statusIndicator = document.getElementById('statusIndicator');
    const statusDot = statusIndicator?.querySelector('.status-dot') as HTMLElement;
    const statusText = statusIndicator?.querySelector('.status-text') as HTMLElement;

    if (this.settings.enabled) {
      statusDot.style.background = '#10b981';
      statusText.textContent = 'Active';
    } else {
      statusDot.style.background = '#ef4444';
      statusText.textContent = 'Disabled';
    }
  }

  private updateThresholdDisplay(value: number): void {
    const thresholdValue = document.getElementById('thresholdValue');
    if (thresholdValue) {
      thresholdValue.textContent = `${Math.round(value * 100)}%`;
    }
  }

  private async saveSettings(): Promise<void> {
    return new Promise((resolve) => {
      chrome.storage.sync.set(this.settings, resolve);
    });
  }

  private async updateStats(newStats: Partial<ExtensionStats>): Promise<void> {
    this.stats = { ...this.stats, ...newStats };
    
    return new Promise((resolve) => {
      chrome.storage.local.set(this.stats, () => {
        this.updateUI();
        resolve();
      });
    });
  }
}

// Initialize popup when DOM is ready
document.addEventListener('DOMContentLoaded', () => {
  new SentinelPopup();
});
