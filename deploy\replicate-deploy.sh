#!/bin/bash

# Project Sentinel - Replicate Deployment Script
# Deploys GenConViT inference service to Replicate

set -e

echo "🛡️ Project Sentinel - Deploying to Replicate"

# Check if cog is installed
if ! command -v cog &> /dev/null; then
    echo "❌ Cog is not installed. Please install it first:"
    echo "   pip install cog"
    exit 1
fi

# Check if we're in the right directory
if [ ! -f "services/inference-worker/cog.yaml" ]; then
    echo "❌ Please run this script from the project root directory"
    exit 1
fi

# Navigate to inference worker directory
cd services/inference-worker

echo "📦 Building Replicate model..."

# Build the model
cog build

echo "🚀 Pushing to Replicate..."

# Push to Replicate (you'll need to set your username)
REPLICATE_USERNAME=${REPLICATE_USERNAME:-"your-username"}
MODEL_NAME="sentinel-deepfake-detector"

cog push r8.im/$REPLICATE_USERNAME/$MODEL_NAME

echo "✅ Deployment complete!"
echo "🔗 Model URL: https://replicate.com/$REPLICATE_USERNAME/$MODEL_NAME"
echo ""
echo "📝 Next steps:"
echo "1. Update the API_BASE_URL in services/api/api/index.py"
echo "2. Update INFERENCE_SERVICE_URL environment variable"
echo "3. Deploy the backend API to Vercel"

cd ../..

echo "🎯 Project Sentinel deployment ready for hackathon!"
