#!/bin/bash

# Project Sentinel - Quick Start Script
# Launches the complete system for hackathon demo

set -e

echo "🛡️ PROJECT SENTINEL - QUICK START"
echo "=================================="
echo ""

# Check prerequisites
echo "🔍 Checking prerequisites..."

if ! command -v docker &> /dev/null; then
    echo "❌ Docker is not installed. Please install Docker first."
    exit 1
fi

if ! command -v docker-compose &> /dev/null; then
    echo "❌ Docker Compose is not installed. Please install Docker Compose first."
    exit 1
fi

if ! command -v node &> /dev/null; then
    echo "❌ Node.js is not installed. Please install Node.js 18+ first."
    exit 1
fi

echo "✅ Prerequisites check passed!"
echo ""

# Setup environment
echo "🔧 Setting up environment..."
if [ ! -f .env ]; then
    cp .env.example .env
    echo "📝 Created .env file from template. Please update with your values."
fi

# Download models if not present
echo "📥 Checking GenConViT model weights..."
if [ ! -f "services/inference-worker/weight/genconvit_ed_inference.pth" ]; then
    echo "⬇️ Downloading model weights..."
    cd services/inference-worker
    python download_models.py
    cd ../..
else
    echo "✅ Model weights already present"
fi

# Build services
echo "🏗️ Building services..."
echo "Building AI inference service..."
cd services/inference-worker
docker build -t sentinel-inference . --quiet
cd ../..

echo "Installing API dependencies..."
cd services/api
npm install --silent
cd ../..

echo "Building browser extension..."
cd apps/extension
npm install --silent
npm run build
cd ../..

echo "✅ Build complete!"
echo ""

# Start services
echo "🚀 Starting Project Sentinel services..."
docker-compose up -d

echo ""
echo "⏳ Waiting for services to start..."
sleep 10

# Check service health
echo "🏥 Checking service health..."

# Check inference service
if curl -s http://localhost:8000/health > /dev/null; then
    echo "✅ AI Inference Service: Running"
else
    echo "⚠️ AI Inference Service: Starting up..."
fi

# Check API service
if curl -s http://localhost:3000/health > /dev/null; then
    echo "✅ Backend API: Running"
else
    echo "⚠️ Backend API: Starting up..."
fi

echo ""
echo "🎯 PROJECT SENTINEL IS READY!"
echo "=============================="
echo ""
echo "📊 Service URLs:"
echo "   • AI Inference: http://localhost:8000"
echo "   • Backend API:  http://localhost:3000"
echo "   • Redis Cache:  localhost:6379"
echo ""
echo "🔧 Browser Extension:"
echo "   1. Open Chrome and go to chrome://extensions/"
echo "   2. Enable 'Developer mode'"
echo "   3. Click 'Load unpacked'"
echo "   4. Select: apps/extension/dist/"
echo ""
echo "🧪 Test the system:"
echo "   make test"
echo ""
echo "📊 View logs:"
echo "   make logs"
echo ""
echo "🛑 Stop services:"
echo "   make stop"
echo ""
echo "🏆 Ready for hackathon demo! Good luck!"
