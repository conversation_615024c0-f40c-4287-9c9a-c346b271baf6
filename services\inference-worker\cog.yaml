# Replicate deployment configuration for Project Sentinel
build:
  gpu: true
  cuda: "11.8"
  python_version: "3.9"
  python_packages:
    - torch>=1.12.0
    - torchvision>=0.13.0
    - timm==0.6.5
    - opencv-python>=4.5.0
    - face-recognition==1.3.0
    - dlib>=19.22.0
    - albumentations==1.3.0
    - decord==0.6.0
    - numpy>=1.21.0
    - Pillow>=8.3.0
    - tqdm>=4.62.0
    - fastapi>=0.68.0
    - uvicorn[standard]>=0.15.0
    - python-multipart>=0.0.5
    - httpx>=0.24.0
    - aiofiles>=0.7.0
    - pyyaml>=5.4.0
    - python-dotenv>=0.19.0
  system_packages:
    - build-essential
    - cmake
    - libopencv-dev
    - libdlib-dev
    - libboost-all-dev
    - wget
    - curl

predict: "predict.py:Predictor"
