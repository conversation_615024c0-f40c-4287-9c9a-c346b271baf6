"""
Simplified GenConViT Model for Project Sentinel
Based on the original GenConViT implementation
"""

import torch
import torch.nn as nn
from .genconvit_ed import GenConViTED
from .genconvit_vae import GenConViTVAE

class GenConViT(nn.Module):
    """
    GenConViT: Generative Convolutional Vision Transformer
    Combines Autoencoder (ED) and Variational Autoencoder (VAE) networks
    """

    def __init__(self, config, ed, vae, net, fp16):
        super(GenConViT, self).__init__()
        self.net = net
        self.fp16 = fp16
        
        if self.net == 'ed':
            self._load_ed_network(config, ed)
        elif self.net == 'vae':
            self._load_vae_network(config, vae)
        else:
            # Load both networks (genconvit mode)
            self._load_both_networks(config, ed, vae)

    def _load_ed_network(self, config, ed_weight):
        """Load Autoencoder network"""
        try:
            self.model_ed = GenConViTED(config)
            checkpoint = torch.load(f'weight/{ed_weight}.pth', map_location='cpu')
            
            if 'state_dict' in checkpoint:
                self.model_ed.load_state_dict(checkpoint['state_dict'])
            else:
                self.model_ed.load_state_dict(checkpoint)

            self.model_ed.eval()
            if self.fp16:
                self.model_ed.half()
                
        except FileNotFoundError:
            raise Exception(f"Error: weight/{ed_weight}.pth file not found.")

    def _load_vae_network(self, config, vae_weight):
        """Load Variational Autoencoder network"""
        try:
            self.model_vae = GenConViTVAE(config)
            checkpoint = torch.load(f'weight/{vae_weight}.pth', map_location='cpu')
            
            if 'state_dict' in checkpoint:
                self.model_vae.load_state_dict(checkpoint['state_dict'])
            else:
                self.model_vae.load_state_dict(checkpoint)
                
            self.model_vae.eval()
            if self.fp16:
                self.model_vae.half()
                
        except FileNotFoundError:
            raise Exception(f"Error: weight/{vae_weight}.pth file not found.")

    def _load_both_networks(self, config, ed_weight, vae_weight):
        """Load both ED and VAE networks"""
        try:
            self.model_ed = GenConViTED(config)
            self.model_vae = GenConViTVAE(config)
            
            # Load ED weights
            checkpoint_ed = torch.load(f'weight/{ed_weight}.pth', map_location='cpu')
            if 'state_dict' in checkpoint_ed:
                self.model_ed.load_state_dict(checkpoint_ed['state_dict'])
            else:
                self.model_ed.load_state_dict(checkpoint_ed)
            
            # Load VAE weights
            checkpoint_vae = torch.load(f'weight/{vae_weight}.pth', map_location='cpu')
            if 'state_dict' in checkpoint_vae:
                self.model_vae.load_state_dict(checkpoint_vae['state_dict'])
            else:
                self.model_vae.load_state_dict(checkpoint_vae)
            
            self.model_ed.eval()
            self.model_vae.eval()
            
            if self.fp16:
                self.model_ed.half()
                self.model_vae.half()
                
        except FileNotFoundError as e:
            raise Exception(f"Error: Model weights file not found: {e}")

    def forward(self, x):
        """Forward pass through the model"""
        if self.net == 'ed':
            return self.model_ed(x)
        elif self.net == 'vae':
            x, _ = self.model_vae(x)
            return x
        else:
            # Combine both networks
            x1 = self.model_ed(x)
            x2, _ = self.model_vae(x)
            return torch.cat((x1, x2), dim=0)
