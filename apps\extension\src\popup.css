/* Project Sentinel - Popup Styles */

* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: #333;
  width: 380px;
  height: 600px;
  overflow: hidden;
}

.popup-container {
  height: 100%;
  display: flex;
  flex-direction: column;
  background: white;
  border-radius: 12px;
  margin: 8px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
}

.popup-header {
  background: linear-gradient(135deg, #1e3a8a 0%, #3730a3 100%);
  color: white;
  padding: 20px;
  border-radius: 12px 12px 0 0;
}

.logo {
  display: flex;
  align-items: center;
  gap: 12px;
  margin-bottom: 12px;
}

.shield-icon {
  font-size: 24px;
}

.logo h1 {
  font-size: 20px;
  font-weight: 600;
}

.status-indicator {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 14px;
}

.status-dot {
  width: 8px;
  height: 8px;
  background: #10b981;
  border-radius: 50%;
  animation: pulse 2s infinite;
}

.popup-main {
  flex: 1;
  padding: 20px;
  overflow-y: auto;
}

.stats-section,
.controls-section,
.threshold-section,
.recent-activity {
  margin-bottom: 24px;
}

.stats-section h2,
.controls-section h2,
.threshold-section h2,
.recent-activity h2 {
  font-size: 16px;
  font-weight: 600;
  margin-bottom: 12px;
  color: #374151;
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 12px;
}

.stat-item {
  text-align: center;
  padding: 12px;
  background: #f8fafc;
  border-radius: 8px;
  border: 1px solid #e2e8f0;
}

.stat-number {
  display: block;
  font-size: 24px;
  font-weight: 700;
  color: #1e40af;
}

.stat-label {
  font-size: 12px;
  color: #64748b;
}

.control-item {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 12px 0;
  border-bottom: 1px solid #e2e8f0;
}

.control-item:last-child {
  border-bottom: none;
}

.toggle-switch {
  position: relative;
  width: 44px;
  height: 24px;
}

.toggle-switch input {
  opacity: 0;
  width: 0;
  height: 0;
}

.toggle-slider {
  position: absolute;
  cursor: pointer;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: #cbd5e1;
  transition: 0.3s;
  border-radius: 24px;
}

.toggle-slider:before {
  position: absolute;
  content: "";
  height: 18px;
  width: 18px;
  left: 3px;
  bottom: 3px;
  background-color: white;
  transition: 0.3s;
  border-radius: 50%;
}

input:checked + .toggle-slider {
  background-color: #3b82f6;
}

input:checked + .toggle-slider:before {
  transform: translateX(20px);
}

.control-info {
  flex: 1;
}

.control-label {
  display: block;
  font-weight: 500;
  color: #374151;
}

.control-description {
  display: block;
  font-size: 12px;
  color: #64748b;
}

.threshold-control {
  display: flex;
  align-items: center;
  gap: 16px;
}

#thresholdSlider {
  flex: 1;
  height: 6px;
  border-radius: 3px;
  background: #e2e8f0;
  outline: none;
  -webkit-appearance: none;
}

#thresholdSlider::-webkit-slider-thumb {
  -webkit-appearance: none;
  appearance: none;
  width: 20px;
  height: 20px;
  border-radius: 50%;
  background: #3b82f6;
  cursor: pointer;
}

.threshold-display {
  text-align: center;
  min-width: 60px;
}

#thresholdValue {
  display: block;
  font-size: 18px;
  font-weight: 600;
  color: #1e40af;
}

.threshold-label {
  font-size: 11px;
  color: #64748b;
}

.activity-list {
  max-height: 120px;
  overflow-y: auto;
}

.activity-item {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 0;
  border-bottom: 1px solid #f1f5f9;
  font-size: 13px;
}

.activity-item:last-child {
  border-bottom: none;
}

.activity-icon {
  font-size: 14px;
}

.activity-text {
  flex: 1;
  color: #374151;
}

.activity-time {
  color: #64748b;
  font-size: 11px;
}

.popup-footer {
  padding: 16px 20px;
  background: #f8fafc;
  border-top: 1px solid #e2e8f0;
  border-radius: 0 0 12px 12px;
}

.footer-info {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 12px;
  color: #64748b;
  margin-bottom: 8px;
}

.separator {
  color: #cbd5e1;
}

.footer-links {
  display: flex;
  gap: 16px;
}

.footer-links a {
  font-size: 12px;
  color: #3b82f6;
  text-decoration: none;
}

.footer-links a:hover {
  text-decoration: underline;
}

/* Animations */
@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}

/* Scrollbar styling */
::-webkit-scrollbar {
  width: 4px;
}

::-webkit-scrollbar-track {
  background: #f1f5f9;
}

::-webkit-scrollbar-thumb {
  background: #cbd5e1;
  border-radius: 2px;
}

::-webkit-scrollbar-thumb:hover {
  background: #94a3b8;
}
