@echo off
REM Project Sentinel - Quick Start Script for Windows
REM Launches the complete system for hackathon demo

echo 🛡️ PROJECT SENTINEL - QUICK START
echo ==================================
echo.

REM Check prerequisites
echo 🔍 Checking prerequisites...

where docker >nul 2>nul
if %errorlevel% neq 0 (
    echo ❌ Docker is not installed. Please install Docker Desktop first.
    pause
    exit /b 1
)

where node >nul 2>nul
if %errorlevel% neq 0 (
    echo ❌ Node.js is not installed. Please install Node.js 18+ first.
    pause
    exit /b 1
)

echo ✅ Prerequisites check passed!
echo.

REM Setup environment
echo 🔧 Setting up environment...
if not exist .env (
    copy .env.example .env
    echo 📝 Created .env file from template. Please update with your values.
)

REM Build services
echo 🏗️ Building services...
echo Building AI inference service...
cd services\inference-worker
docker build -t sentinel-inference . --quiet
cd ..\..

echo Installing API dependencies...
cd services\api
call npm install --silent
cd ..\..

echo Building browser extension...
cd apps\extension
call npm install --silent
call npm run build
cd ..\..

echo ✅ Build complete!
echo.

REM Start services
echo 🚀 Starting Project Sentinel services...
docker-compose up -d

echo.
echo ⏳ Waiting for services to start...
timeout /t 15 /nobreak >nul

echo.
echo 🎯 PROJECT SENTINEL IS READY!
echo ==============================
echo.
echo 📊 Service URLs:
echo    • AI Inference: http://localhost:8000
echo    • Backend API:  http://localhost:3000
echo    • Redis Cache:  localhost:6379
echo.
echo 🔧 Browser Extension:
echo    1. Open Chrome and go to chrome://extensions/
echo    2. Enable 'Developer mode'
echo    3. Click 'Load unpacked'
echo    4. Select: apps\extension\dist\
echo.
echo 🧪 Test the system:
echo    python test_system.py
echo.
echo 📊 View logs:
echo    docker-compose logs -f
echo.
echo 🛑 Stop services:
echo    docker-compose down
echo.
echo 🏆 Ready for hackathon demo! Good luck!
pause
