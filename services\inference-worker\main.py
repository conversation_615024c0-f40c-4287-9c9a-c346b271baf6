"""
Project Sentinel - GenConViT Inference Service
High-performance deepfake detection API using GenConViT model
"""

import os
import asyncio
import tempfile
import logging
import time
from typing import Optional, Dict, Any
from contextlib import asynccontextmanager

import torch
import httpx
import uvicorn
from fastapi import FastAPI, HTTPException, BackgroundTasks
from fastapi.middleware.cors import CORSMiddleware
from pydantic import BaseModel, HttpUrl

from genconvit_inference import GenConViTInference

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Global model instance
model_instance: Optional[GenConViTInference] = None

class AnalysisRequest(BaseModel):
    video_url: HttpUrl
    callback_url: Optional[HttpUrl] = None
    job_id: str
    num_frames: int = 15

class AnalysisResponse(BaseModel):
    job_id: str
    status: str
    deepfake_probability: Optional[float] = None
    confidence_score: Optional[float] = None
    processing_time: Optional[float] = None
    frames_analyzed: Optional[int] = None
    device_used: Optional[str] = None
    error: Optional[str] = None

@asynccontextmanager
async def lifespan(app: FastAPI):
    """Load model on startup"""
    global model_instance
    logger.info("🚀 Starting Project Sentinel Inference Service...")
    logger.info("Loading GenConViT model...")

    try:
        model_instance = GenConViTInference()
        await model_instance.load_model()
        logger.info("✅ GenConViT model loaded successfully")
        logger.info(f"🔧 Device: {model_instance.device}")
        logger.info(f"⚡ FP16 enabled: {model_instance.fp16}")
    except Exception as e:
        logger.error(f"❌ Failed to load model: {e}")
        raise

    yield

    # Cleanup
    logger.info("🛑 Shutting down inference service")

app = FastAPI(
    title="Project Sentinel - GenConViT Inference API",
    description="Real-time deepfake detection using GenConViT",
    version="1.0.0",
    lifespan=lifespan
)

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

@app.get("/")
async def root():
    """Root endpoint with service info"""
    return {
        "service": "Project Sentinel - GenConViT Inference API",
        "version": "1.0.0",
        "status": "operational",
        "model_loaded": model_instance is not None
    }

@app.get("/health")
async def health_check():
    """Health check endpoint"""
    return {
        "status": "healthy",
        "model_loaded": model_instance is not None,
        "device": str(model_instance.device) if model_instance else "unknown",
        "fp16_enabled": model_instance.fp16 if model_instance else False,
        "timestamp": time.time()
    }

@app.post("/predict", response_model=AnalysisResponse)
async def predict_deepfake(
    request: AnalysisRequest,
    background_tasks: BackgroundTasks
):
    """
    Analyze video for deepfake content
    Returns immediate response and processes asynchronously
    """
    if not model_instance:
        raise HTTPException(status_code=503, detail="Model not loaded")

    logger.info(f"🎬 Received analysis request for job {request.job_id}")

    # Start background processing
    background_tasks.add_task(
        process_video_async,
        str(request.video_url),
        str(request.callback_url) if request.callback_url else None,
        request.job_id,
        request.num_frames
    )

    return AnalysisResponse(
        job_id=request.job_id,
        status="processing"
    )

@app.post("/predict-sync", response_model=AnalysisResponse)
async def predict_deepfake_sync(request: AnalysisRequest):
    """
    Synchronous prediction endpoint for testing
    """
    if not model_instance:
        raise HTTPException(status_code=503, detail="Model not loaded")

    try:
        logger.info(f"🎬 Synchronous analysis for job {request.job_id}")

        # Download video
        video_path = await download_video(str(request.video_url))

        # Run inference
        result = await model_instance.predict_video(video_path, request.num_frames)

        # Cleanup
        os.unlink(video_path)

        return AnalysisResponse(
            job_id=request.job_id,
            status="completed",
            **result
        )

    except Exception as e:
        logger.error(f"❌ Sync prediction failed for {request.job_id}: {e}")
        raise HTTPException(status_code=500, detail=str(e))

async def process_video_async(
    video_url: str,
    callback_url: Optional[str],
    job_id: str,
    num_frames: int
):
    """Process video analysis in background"""
    try:
        logger.info(f"🔄 Processing video {job_id}: {video_url}")

        # Download video to temporary file
        video_path = await download_video(video_url)

        # Run inference
        result = await model_instance.predict_video(video_path, num_frames)

        # Cleanup temporary file
        os.unlink(video_path)

        # Send result to callback URL if provided
        if callback_url:
            await send_callback(callback_url, job_id, result)

        logger.info(f"✅ Completed processing {job_id}: deepfake_prob={result.get('deepfake_probability', 'N/A'):.3f}")

    except Exception as e:
        logger.error(f"❌ Error processing {job_id}: {e}")
        if callback_url:
            await send_callback(callback_url, job_id, {"error": str(e)})

async def download_video(video_url: str) -> str:
    """Download video to temporary file"""
    try:
        async with httpx.AsyncClient(timeout=60.0) as client:
            logger.info(f"📥 Downloading video: {video_url}")
            response = await client.get(video_url)
            response.raise_for_status()

            # Create temporary file with appropriate extension
            file_extension = '.mp4'
            if video_url.endswith(('.avi', '.mov', '.mkv', '.webm')):
                file_extension = '.' + video_url.split('.')[-1]

            with tempfile.NamedTemporaryFile(delete=False, suffix=file_extension) as tmp_file:
                tmp_file.write(response.content)
                logger.info(f"📁 Video saved to: {tmp_file.name}")
                return tmp_file.name

    except Exception as e:
        logger.error(f"❌ Failed to download video: {e}")
        raise

async def send_callback(callback_url: str, job_id: str, result: Dict[str, Any]):
    """Send analysis result to callback URL"""
    try:
        async with httpx.AsyncClient(timeout=30.0) as client:
            payload = {
                "job_id": job_id,
                "status": "completed" if "error" not in result else "failed",
                "result": result,
                "timestamp": time.time()
            }

            logger.info(f"📤 Sending callback for {job_id} to {callback_url}")
            await client.post(callback_url, json=payload)
            logger.info(f"✅ Callback sent successfully for {job_id}")

    except Exception as e:
        logger.error(f"❌ Failed to send callback for {job_id}: {e}")

if __name__ == "__main__":
    uvicorn.run(
        "main:app",
        host="0.0.0.0",
        port=8000,
        log_level="info",
        reload=False
    )
