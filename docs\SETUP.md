# Project Sentinel - Setup Guide 🛡️

## Prerequisites

### Required Software
- **Docker & Docker Compose** (for AI inference service)
- **Node.js 18+** (for extension and API development)
- **Python 3.9+** (for AI service development)
- **Git** (for cloning repositories)

### Required Accounts
- **Replicate Account** (for GPU inference hosting)
- **Vercel Account** (for API hosting)
- **Hugging Face Account** (optional, for backup deployment)

## Step-by-Step Setup

### 1. Clone and Initialize Project

```bash
git clone <your-repository>
cd HACKTHON

# Make deployment scripts executable
chmod +x deploy/*.sh
```

### 2. Set Up AI Inference Service

```bash
cd services/inference-worker

# Build Docker container
docker build -t sentinel-inference .

# Test locally (requires GPU for optimal performance)
docker run -p 8000:8000 --gpus all sentinel-inference

# Test health endpoint
curl http://localhost:8000/health
```

**Expected Response:**
```json
{
  "status": "healthy",
  "model_loaded": true,
  "device": "cuda:0"
}
```

### 3. Deploy to Replicate

```bash
# Install Cog (Replicate's deployment tool)
pip install cog

# Set your Replicate username
export REPLICATE_USERNAME="your-username"

# Deploy inference service
./deploy/replicate-deploy.sh
```

### 4. Set Up Backend API

```bash
cd services/api

# Install dependencies
npm install

# Set environment variables
export INFERENCE_SERVICE_URL="https://api.replicate.com/v1/predictions"
export REPLICATE_API_TOKEN="your-replicate-token"

# Test locally
npm run dev
```

### 5. Deploy API to Vercel

```bash
# Install Vercel CLI
npm install -g vercel

# Deploy
./deploy/vercel-deploy.sh
```

### 6. Build Browser Extension

```bash
cd apps/extension

# Install dependencies
npm install

# Update API URLs in source files
# Edit src/content.ts and src/background.ts
# Replace 'https://sentinel-api.vercel.app' with your Vercel URL

# Build extension
npm run build
```

### 7. Install Browser Extension

1. Open Chrome and go to `chrome://extensions/`
2. Enable "Developer mode"
3. Click "Load unpacked"
4. Select the `apps/extension/dist` folder
5. The Sentinel shield icon should appear in your toolbar

## Configuration

### Environment Variables

**Inference Service:**
- `TORCH_HOME`: PyTorch model cache directory
- `PYTHONPATH`: Python module path

**Backend API:**
- `INFERENCE_SERVICE_URL`: URL of your deployed inference service
- `KV_REST_API_URL`: Vercel KV store URL
- `KV_REST_API_TOKEN`: Vercel KV store token

**Browser Extension:**
- Update `apiBaseUrl` in content.ts and background.ts

### Model Weights

The inference service automatically downloads pre-trained GenConViT weights:
- `genconvit_ed_inference.pth` (~2.6GB)
- `genconvit_vae_inference.pth` (~2.6GB)

## Testing

### Test AI Inference Service
```bash
curl -X POST http://localhost:8000/predict-sync \
  -H "Content-Type: application/json" \
  -d '{
    "video_url": "https://example.com/test-video.mp4",
    "job_id": "test-123",
    "num_frames": 10
  }'
```

### Test Backend API
```bash
curl -X POST http://localhost:3000/api/analyze \
  -H "Content-Type: application/json" \
  -d '{
    "video_url": "https://example.com/test-video.mp4",
    "num_frames": 15
  }'
```

### Test Browser Extension
1. Navigate to YouTube, Twitter, or LinkedIn
2. Find a video post
3. Look for Sentinel loading indicator
4. Wait for analysis result overlay

## Troubleshooting

### Common Issues

**Model Loading Fails:**
- Ensure model weights are downloaded correctly
- Check available GPU memory (models require ~6GB)
- Verify PyTorch CUDA installation

**Extension Not Working:**
- Check browser console for errors
- Verify API URLs are correct
- Ensure CORS is properly configured

**API Timeouts:**
- Increase timeout values in httpx clients
- Check inference service health
- Monitor Replicate usage limits

### Performance Optimization

**For CPU-only deployment:**
- Set `fp16=False` in model initialization
- Reduce `num_frames` to 10 or fewer
- Use smaller model variants (tiny instead of base)

**For GPU deployment:**
- Enable fp16 for memory efficiency
- Batch multiple requests if possible
- Use model caching for faster startup

## Next Steps

1. **Customize Detection Threshold**: Adjust `alertThreshold` in extension settings
2. **Add More Platforms**: Extend content script for additional social media sites
3. **Enhance UI**: Improve alert styling and user experience
4. **Add Analytics**: Track detection statistics and performance metrics
5. **Scale Infrastructure**: Set up auto-scaling for high-traffic scenarios

---

**Ready to protect investors from deepfake fraud! 🚀**
