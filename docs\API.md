# Project Sentinel - API Documentation 📡

## Backend API Endpoints

### Base URL
- **Development**: `http://localhost:3000`
- **Production**: `https://sentinel-api.vercel.app`

### Authentication
Currently no authentication required for hackathon demo. In production, implement API keys.

## Endpoints

### 1. Health Check
```http
GET /health
```

**Response:**
```json
{
  "status": "healthy",
  "inference_service_healthy": true,
  "cache_size": 42,
  "timestamp": **********.0
}
```

### 2. Submit Video Analysis
```http
POST /api/analyze
Content-Type: application/json

{
  "video_url": "https://example.com/video.mp4",
  "num_frames": 15
}
```

**Response:**
```json
{
  "job_id": "550e8400-e29b-41d4-a716-446655440000",
  "status": "processing",
  "message": "Video submitted for analysis"
}
```

### 3. Check Analysis Status
```http
GET /api/status/{job_id}
```

**Response (Processing):**
```json
{
  "job_id": "550e8400-e29b-41d4-a716-446655440000",
  "status": "processing",
  "created_at": **********.0,
  "completed_at": null
}
```

**Response (Completed):**
```json
{
  "job_id": "550e8400-e29b-41d4-a716-446655440000",
  "status": "completed",
  "deepfake_probability": 0.85,
  "confidence_score": 0.92,
  "processing_time": 23.5,
  "frames_analyzed": 15,
  "device_used": "cuda:0",
  "created_at": **********.0,
  "completed_at": **********.5
}
```

### 4. Webhook Handler (Internal)
```http
POST /api/webhook
Content-Type: application/json

{
  "job_id": "550e8400-e29b-41d4-a716-446655440000",
  "status": "completed",
  "result": {
    "deepfake_probability": 0.85,
    "confidence_score": 0.92,
    "processing_time": 23.5,
    "frames_analyzed": 15,
    "device_used": "cuda:0"
  },
  "timestamp": **********.5
}
```

## AI Inference Service Endpoints

### Base URL
- **Development**: `http://localhost:8000`
- **Production**: `https://api.replicate.com/v1/predictions`

### 1. Health Check
```http
GET /health
```

**Response:**
```json
{
  "status": "healthy",
  "model_loaded": true,
  "device": "cuda:0",
  "fp16_enabled": true,
  "timestamp": **********.0
}
```

### 2. Async Prediction
```http
POST /predict
Content-Type: application/json

{
  "video_url": "https://example.com/video.mp4",
  "callback_url": "https://sentinel-api.vercel.app/api/webhook",
  "job_id": "550e8400-e29b-41d4-a716-446655440000",
  "num_frames": 15
}
```

### 3. Sync Prediction (Testing)
```http
POST /predict-sync
Content-Type: application/json

{
  "video_url": "https://example.com/video.mp4",
  "job_id": "test-123",
  "num_frames": 15
}
```

## Error Responses

### 400 Bad Request
```json
{
  "detail": "Invalid video URL format"
}
```

### 404 Not Found
```json
{
  "detail": "Job not found"
}
```

### 500 Internal Server Error
```json
{
  "detail": "Model inference failed: CUDA out of memory"
}
```

### 503 Service Unavailable
```json
{
  "detail": "Model not loaded"
}
```

## Rate Limits

- **Analysis Submissions**: 10 requests per minute per IP
- **Status Checks**: 60 requests per minute per IP
- **Webhook Calls**: No limit (internal)

## Response Times

- **Submit Analysis**: < 1 second
- **Status Check**: < 0.5 seconds
- **Video Processing**: 10-30 seconds (depending on video length and hardware)

## Usage Examples

### JavaScript/TypeScript
```typescript
// Submit video for analysis
const response = await fetch('https://sentinel-api.vercel.app/api/analyze', {
  method: 'POST',
  headers: { 'Content-Type': 'application/json' },
  body: JSON.stringify({
    video_url: 'https://example.com/video.mp4',
    num_frames: 15
  })
});

const { job_id } = await response.json();

// Poll for results
const pollResult = async () => {
  const statusResponse = await fetch(`https://sentinel-api.vercel.app/api/status/${job_id}`);
  const status = await statusResponse.json();
  
  if (status.status === 'completed') {
    console.log('Deepfake probability:', status.deepfake_probability);
  } else if (status.status === 'processing') {
    setTimeout(pollResult, 5000); // Poll again in 5 seconds
  }
};

pollResult();
```

### Python
```python
import requests
import time

# Submit analysis
response = requests.post('https://sentinel-api.vercel.app/api/analyze', json={
    'video_url': 'https://example.com/video.mp4',
    'num_frames': 15
})

job_id = response.json()['job_id']

# Poll for results
while True:
    status_response = requests.get(f'https://sentinel-api.vercel.app/api/status/{job_id}')
    status = status_response.json()
    
    if status['status'] == 'completed':
        print(f"Deepfake probability: {status['deepfake_probability']}")
        break
    elif status['status'] == 'failed':
        print(f"Analysis failed: {status.get('error', 'Unknown error')}")
        break
    
    time.sleep(5)
```

### cURL
```bash
# Submit analysis
curl -X POST https://sentinel-api.vercel.app/api/analyze \
  -H "Content-Type: application/json" \
  -d '{"video_url": "https://example.com/video.mp4", "num_frames": 15}'

# Check status
curl https://sentinel-api.vercel.app/api/status/550e8400-e29b-41d4-a716-446655440000
```

## Browser Extension Integration

The browser extension automatically:
1. Detects videos on supported platforms
2. Submits them for analysis via `/api/analyze`
3. Polls `/api/status/{job_id}` for results
4. Displays visual alerts based on deepfake probability

### Supported Platforms
- Twitter/X
- YouTube  
- LinkedIn
- Telegram Web

---

**API ready for real-time deepfake detection! ⚡**
