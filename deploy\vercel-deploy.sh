#!/bin/bash

# Project Sentinel - Vercel Deployment Script
# Deploys backend API to Vercel

set -e

echo "🛡️ Project Sentinel - Deploying API to Vercel"

# Check if Vercel CLI is installed
if ! command -v vercel &> /dev/null; then
    echo "❌ Vercel CLI is not installed. Please install it first:"
    echo "   npm install -g vercel"
    exit 1
fi

# Navigate to API directory
cd services/api

echo "📦 Installing dependencies..."
npm install

echo "🔧 Setting up environment variables..."
echo "Please set the following environment variables in Vercel:"
echo "- INFERENCE_SERVICE_URL: Your Replicate model URL"
echo "- KV_REST_API_URL: Vercel KV REST API URL"
echo "- KV_REST_API_TOKEN: Vercel KV REST API Token"

echo "🚀 Deploying to Vercel..."

# Deploy to Vercel
vercel --prod

echo "✅ API deployment complete!"
echo ""
echo "📝 Next steps:"
echo "1. Update the apiBaseUrl in apps/extension/src/content.ts"
echo "2. Update the apiBaseUrl in apps/extension/src/background.ts"
echo "3. Build and install the browser extension"

cd ../..

echo "🎯 Backend API ready for hackathon!"
