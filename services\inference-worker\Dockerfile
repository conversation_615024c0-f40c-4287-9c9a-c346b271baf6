# Project Sentinel - GenConViT Inference Service
# Optimized Docker container for deepfake detection

FROM python:3.9-slim

# Install system dependencies for OpenCV, dlib, and face_recognition
RUN apt-get update && apt-get install -y \
    build-essential \
    cmake \
    libopencv-dev \
    libdlib-dev \
    libboost-all-dev \
    wget \
    git \
    curl \
    && rm -rf /var/lib/apt/lists/*

# Set working directory
WORKDIR /app

# Copy requirements first for better caching
COPY requirements.txt .

# Install Python dependencies
RUN pip install --no-cache-dir -r requirements.txt

# Clone GenConViT repository and copy source
RUN git clone https://github.com/erprogs/GenConViT.git /tmp/genconvit && \
    cp -r /tmp/genconvit/model ./original_model && \
    cp -r /tmp/genconvit/dataset . && \
    rm -rf /tmp/genconvit

# Copy our optimized model implementation
COPY model/ ./model/
COPY dataset/ ./dataset/

# Create weight directory and download pre-trained models
RUN mkdir -p weight && \
    wget -O weight/genconvit_ed_inference.pth https://huggingface.co/Deressa/GenConViT/resolve/main/genconvit_ed_inference.pth && \
    wget -O weight/genconvit_vae_inference.pth https://huggingface.co/Deressa/GenConViT/resolve/main/genconvit_vae_inference.pth

# Copy our inference service code
COPY *.py ./

# Expose port
EXPOSE 8000

# Health check
HEALTHCHECK --interval=30s --timeout=30s --start-period=5s --retries=3 \
    CMD curl -f http://localhost:8000/health || exit 1

# Run the inference service
CMD ["uvicorn", "main:app", "--host", "0.0.0.0", "--port", "8000"]
