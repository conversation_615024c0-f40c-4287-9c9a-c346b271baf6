"""
Project Sentinel - Model Download Script
Downloads pre-trained GenConViT weights from Hugging Face
"""

import os
import requests
from tqdm import tqdm

def download_file(url: str, filename: str):
    """Download file with progress bar"""
    print(f"📥 Downloading {filename}...")
    
    response = requests.get(url, stream=True)
    response.raise_for_status()
    
    total_size = int(response.headers.get('content-length', 0))
    
    with open(filename, 'wb') as file, tqdm(
        desc=filename,
        total=total_size,
        unit='B',
        unit_scale=True,
        unit_divisor=1024,
    ) as pbar:
        for chunk in response.iter_content(chunk_size=8192):
            if chunk:
                file.write(chunk)
                pbar.update(len(chunk))
    
    print(f"✅ Downloaded {filename}")

def main():
    """Download GenConViT model weights"""
    
    # Create weight directory
    os.makedirs('weight', exist_ok=True)
    
    # Model URLs
    models = {
        'genconvit_ed_inference.pth': 'https://huggingface.co/Deressa/GenConViT/resolve/main/genconvit_ed_inference.pth',
        'genconvit_vae_inference.pth': 'https://huggingface.co/Deressa/GenConViT/resolve/main/genconvit_vae_inference.pth'
    }
    
    print("🛡️ Project Sentinel - Downloading GenConViT Models")
    print("=" * 50)
    
    for filename, url in models.items():
        filepath = os.path.join('weight', filename)
        
        if os.path.exists(filepath):
            print(f"⏭️ {filename} already exists, skipping...")
            continue
        
        try:
            download_file(url, filepath)
        except Exception as e:
            print(f"❌ Failed to download {filename}: {e}")
            return False
    
    print("\n🎯 All models downloaded successfully!")
    print("Ready for inference! 🚀")
    return True

if __name__ == "__main__":
    main()
