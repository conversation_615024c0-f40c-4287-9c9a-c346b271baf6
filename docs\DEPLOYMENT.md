# Project Sentinel - Deployment Guide 🚀

## Production Deployment Strategy

### Architecture Overview

```
Internet → Vercel (API) → Replicate (AI) → Results → Browser Extension
```

## 1. AI Inference Service Deployment

### Option A: Replicate (Recommended)

**Advantages:**
- Pay-per-use GPU inference
- Automatic scaling
- No infrastructure management
- Cost: ~$0.01-0.05 per video analysis

**Steps:**
```bash
cd services/inference-worker

# Install Cog
pip install cog

# Login to Replicate
cog login

# Build and push
cog build
cog push r8.im/your-username/sentinel-deepfake-detector
```

### Option B: Hugging Face Spaces (Free Tier)

**Advantages:**
- Free GPU quota
- Easy deployment
- Good for development/testing

**Steps:**
1. Create new Space on Hugging Face
2. Upload inference service files
3. Configure as Gradio app
4. Use Spaces API for inference

### Option C: Google Cloud Run (CPU)

**Advantages:**
- Generous free tier
- Serverless scaling
- Good CPU performance

**Steps:**
```bash
# Build for Cloud Run
docker build -t gcr.io/your-project/sentinel-inference .

# Push to Container Registry
docker push gcr.io/your-project/sentinel-inference

# Deploy to Cloud Run
gcloud run deploy sentinel-inference \
  --image gcr.io/your-project/sentinel-inference \
  --platform managed \
  --region us-central1 \
  --memory 4Gi \
  --cpu 2 \
  --max-instances 10
```

## 2. Backend API Deployment

### Vercel Deployment (Recommended)

```bash
cd services/api

# Install Vercel CLI
npm install -g vercel

# Login to Vercel
vercel login

# Deploy
vercel --prod

# Set environment variables
vercel env add INFERENCE_SERVICE_URL
vercel env add KV_REST_API_URL
vercel env add KV_REST_API_TOKEN
```

### Environment Variables Setup

```bash
# Set your inference service URL
vercel env add INFERENCE_SERVICE_URL "https://api.replicate.com/v1/predictions"

# Set up Vercel KV (for job caching)
vercel env add KV_REST_API_URL "https://your-kv-store.vercel-storage.com"
vercel env add KV_REST_API_TOKEN "your-kv-token"
```

## 3. Browser Extension Distribution

### Chrome Web Store (Production)

1. **Prepare Extension Package:**
```bash
cd apps/extension

# Update API URLs for production
# Edit src/content.ts and src/background.ts
# Replace localhost URLs with production URLs

# Build for production
npm run build

# Create ZIP package
zip -r sentinel-extension.zip dist/
```

2. **Submit to Chrome Web Store:**
- Go to Chrome Developer Dashboard
- Upload sentinel-extension.zip
- Fill out store listing details
- Submit for review

### Developer Mode (Testing)

1. Build extension: `npm run build`
2. Open Chrome → Extensions → Developer mode
3. Load unpacked → Select `dist/` folder

## 4. Monitoring & Analytics

### Set Up Monitoring

```bash
# Add monitoring to API
npm install @vercel/analytics

# Add error tracking
npm install @sentry/node
```

### Performance Monitoring

- **Replicate**: Monitor usage in Replicate dashboard
- **Vercel**: Use Vercel Analytics for API performance
- **Extension**: Chrome Extension Analytics

## 5. Cost Optimization

### Inference Service Costs

**Replicate Pricing (T4 GPU):**
- $0.000225/second
- Average video analysis: 10-30 seconds
- Cost per analysis: $0.002-0.007

**Monthly Estimates:**
- 1,000 analyses: $2-7
- 10,000 analyses: $20-70
- 100,000 analyses: $200-700

### API Hosting Costs

**Vercel:**
- Free tier: 100GB bandwidth, 1000 serverless function invocations
- Pro tier: $20/month for higher limits

### Optimization Strategies

1. **Caching**: Cache results for identical videos
2. **Batching**: Process multiple frames in single request
3. **Compression**: Use video compression before analysis
4. **Throttling**: Limit analysis frequency per user

## 6. Security Considerations

### API Security

```python
# Add rate limiting
from slowapi import Limiter
from slowapi.util import get_remote_address

limiter = Limiter(key_func=get_remote_address)

@app.post("/api/analyze")
@limiter.limit("10/minute")
async def analyze_video(request: Request, ...):
    # Analysis logic
```

### Extension Security

- Validate all video URLs
- Sanitize user inputs
- Use Content Security Policy
- Implement permission scoping

## 7. Scaling Strategy

### Auto-scaling Configuration

**Replicate:**
- Automatic scaling based on demand
- Cold start time: ~10-30 seconds
- Warm instances for faster response

**Vercel:**
- Automatic function scaling
- Edge caching for static content
- Global CDN distribution

### Load Testing

```bash
# Install load testing tool
npm install -g artillery

# Test API endpoints
artillery quick --count 100 --num 10 https://your-api.vercel.app/health
```

## 8. Hackathon Demo Preparation

### Demo Script

1. **Setup (5 minutes):**
   - Show extension installation
   - Demonstrate settings panel
   - Explain technology stack

2. **Live Demo (10 minutes):**
   - Navigate to Twitter/YouTube
   - Show real-time video detection
   - Trigger deepfake alert
   - Explain confidence scores

3. **Technical Deep Dive (10 minutes):**
   - Show architecture diagram
   - Explain GenConViT model
   - Demonstrate API endpoints
   - Show deployment scalability

### Demo Videos

Prepare test videos:
- Real corporate announcement
- Known deepfake example
- Edge cases (low quality, multiple faces)

### Backup Plans

1. **Offline Demo**: Pre-recorded analysis results
2. **Local Deployment**: Docker Compose stack
3. **Mock Service**: Simulated API responses

---

**Project Sentinel is ready to win the hackathon! 🏆**
