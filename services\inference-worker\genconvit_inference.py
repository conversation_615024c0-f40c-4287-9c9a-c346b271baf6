"""
GenConViT Inference Wrapper
Optimized for Project Sentinel deployment
"""

import os
import time
import asyncio
from typing import Dict, Any, List
import logging

import torch
import numpy as np
import cv2
import face_recognition
from decord import VideoReader, cpu
from PIL import Image
import yaml

logger = logging.getLogger(__name__)

class GenConViTInference:
    """Optimized GenConViT inference wrapper for Project Sentinel"""
    
    def __init__(self):
        self.device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
        self.model = None
        self.config = None
        self.fp16 = torch.cuda.is_available()  # Use fp16 only on GPU
        
        logger.info(f"Initializing GenConViT on device: {self.device}")
    
    async def load_model(self):
        """Load GenConViT model and configuration"""
        try:
            # Load configuration
            self.config = self._load_config()

            # Import GenConViT model
            from model.genconvit import GenConViT

            # Initialize model with both ED and VAE networks
            self.model = GenConViT(
                config=self.config,
                ed="genconvit_ed_inference",
                vae="genconvit_vae_inference",
                net="genconvit",  # Use both networks
                fp16=self.fp16
            )

            self.model.to(self.device)
            self.model.eval()

            if self.fp16:
                self.model.half()

            logger.info("✅ GenConViT model loaded successfully")
            logger.info(f"🔧 Device: {self.device}")
            logger.info(f"⚡ FP16: {self.fp16}")

        except Exception as e:
            logger.error(f"❌ Failed to load model: {e}")
            raise
    
    def _load_config(self) -> Dict[str, Any]:
        """Load model configuration"""
        config = {
            "model": {
                "backbone": "convnext_tiny",
                "embedder": "swin_tiny_patch4_window7_224",
                "latent_dims": 12544
            },
            "batch_size": 32,
            "num_classes": 2,
            "img_size": 224
        }
        return config
    
    async def predict_video(self, video_path: str, num_frames: int = 15) -> Dict[str, Any]:
        """
        Predict if video contains deepfake content
        
        Args:
            video_path: Path to video file
            num_frames: Number of frames to analyze
            
        Returns:
            Dictionary with prediction results
        """
        start_time = time.time()
        
        try:
            # Extract and preprocess frames
            frames_tensor = await self._extract_and_preprocess_faces(video_path, num_frames)
            
            if len(frames_tensor) == 0:
                return {
                    "deepfake_probability": 0.5,
                    "confidence_score": 0.0,
                    "processing_time": time.time() - start_time,
                    "error": "No faces detected in video"
                }
            
            # Run inference
            with torch.no_grad():
                prediction = torch.sigmoid(self.model(frames_tensor).squeeze())
                
                # Calculate final prediction
                mean_pred = torch.mean(prediction, dim=0)
                fake_prob = mean_pred[1].item()  # Probability of being fake
                confidence = max(mean_pred[0].item(), mean_pred[1].item())
                
            processing_time = time.time() - start_time
            
            result = {
                "deepfake_probability": fake_prob,
                "confidence_score": confidence,
                "processing_time": processing_time,
                "frames_analyzed": len(frames_tensor),
                "device_used": str(self.device)
            }
            
            logger.info(f"Prediction complete: {fake_prob:.3f} confidence: {confidence:.3f} time: {processing_time:.2f}s")
            return result
            
        except Exception as e:
            logger.error(f"Prediction failed: {e}")
            return {
                "deepfake_probability": 0.5,
                "confidence_score": 0.0,
                "processing_time": time.time() - start_time,
                "error": str(e)
            }
    
    async def _extract_and_preprocess_faces(self, video_path: str, num_frames: int) -> torch.Tensor:
        """Extract faces from video and preprocess for model input"""
        try:
            # Extract frames from video
            frames = self._extract_frames(video_path, num_frames)
            
            # Extract faces from frames
            faces, face_count = self._extract_faces(frames)
            
            if face_count == 0:
                return torch.tensor([])
            
            # Preprocess faces for model input
            return self._preprocess_faces(faces[:face_count])
            
        except Exception as e:
            logger.error(f"Face extraction failed: {e}")
            return torch.tensor([])
    
    def _extract_frames(self, video_path: str, num_frames: int) -> np.ndarray:
        """Extract frames from video using decord"""
        vr = VideoReader(video_path, ctx=cpu(0))
        total_frames = len(vr)
        
        if total_frames == 0:
            raise ValueError("Video contains no frames")
        
        # Sample frames evenly across video
        indices = np.linspace(0, total_frames - 1, min(num_frames, total_frames), dtype=int)
        return vr.get_batch(indices).asnumpy()
    
    def _extract_faces(self, frames: np.ndarray) -> tuple:
        """Extract faces from frames using face_recognition"""
        face_images = np.zeros((len(frames), 224, 224, 3), dtype=np.uint8)
        face_count = 0
        
        # Use HOG for CPU, CNN for GPU
        detection_model = "cnn" if torch.cuda.is_available() else "hog"
        
        for frame in frames:
            if face_count >= len(frames):
                break
                
            # Convert RGB to BGR for face_recognition
            frame_bgr = cv2.cvtColor(frame, cv2.COLOR_RGB2BGR)
            
            # Detect faces
            face_locations = face_recognition.face_locations(
                frame_bgr, 
                number_of_times_to_upsample=0, 
                model=detection_model
            )
            
            # Process first detected face
            if face_locations:
                top, right, bottom, left = face_locations[0]
                face_image = frame_bgr[top:bottom, left:right]
                
                # Resize to model input size
                face_image = cv2.resize(face_image, (224, 224), interpolation=cv2.INTER_AREA)
                face_image = cv2.cvtColor(face_image, cv2.COLOR_BGR2RGB)
                
                face_images[face_count] = face_image
                face_count += 1
        
        return face_images, face_count
    
    def _preprocess_faces(self, faces: np.ndarray) -> torch.Tensor:
        """Preprocess faces for model input"""
        # Convert to tensor and normalize
        faces_tensor = torch.tensor(faces, device=self.device).float()
        faces_tensor = faces_tensor.permute((0, 3, 1, 2))  # NHWC -> NCHW
        
        # Normalize to [0, 1] and apply standard normalization
        faces_tensor = faces_tensor / 255.0
        
        # Apply ImageNet normalization (as used in training)
        mean = torch.tensor([0.485, 0.456, 0.406], device=self.device).view(1, 3, 1, 1)
        std = torch.tensor([0.229, 0.224, 0.225], device=self.device).view(1, 3, 1, 1)
        faces_tensor = (faces_tensor - mean) / std
        
        if self.fp16:
            faces_tensor = faces_tensor.half()
            
        return faces_tensor

    def _normalize_tensor(self, tensor: torch.Tensor) -> torch.Tensor:
        """Apply ImageNet normalization to tensor"""
        mean = torch.tensor([0.485, 0.456, 0.406], device=self.device).view(1, 3, 1, 1)
        std = torch.tensor([0.229, 0.224, 0.225], device=self.device).view(1, 3, 1, 1)
        return (tensor - mean) / std
