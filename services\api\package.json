{"name": "sentinel-api", "version": "1.0.0", "description": "Project Sentinel Backend API - FastAPI Orchestrator", "main": "index.js", "scripts": {"dev": "vercel dev", "build": "echo 'No build step required for Python API'", "deploy": "vercel --prod", "test": "python -m pytest tests/"}, "dependencies": {}, "devDependencies": {"vercel": "^33.0.0"}, "keywords": ["deepfake", "detection", "api", "<PERSON><PERSON><PERSON>", "vercel"], "author": "Project Sentinel Team", "license": "MIT"}