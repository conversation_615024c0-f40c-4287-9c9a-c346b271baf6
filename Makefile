# Project Sentinel - Makefile
# Simplified commands for development and deployment

.PHONY: help setup build test deploy clean

help: ## Show this help message
	@echo "🛡️ Project Sentinel - Available Commands"
	@echo "========================================"
	@grep -E '^[a-zA-Z_-]+:.*?## .*$$' $(MAKEFILE_LIST) | sort | awk 'BEGIN {FS = ":.*?## "}; {printf "\033[36m%-20s\033[0m %s\n", $$1, $$2}'

setup: ## Set up development environment
	@echo "🔧 Setting up Project Sentinel development environment..."
	@echo "Installing AI service dependencies..."
	cd services/inference-worker && pip install -r requirements.txt
	@echo "Installing API dependencies..."
	cd services/api && npm install
	@echo "Installing extension dependencies..."
	cd apps/extension && npm install
	@echo "✅ Setup complete!"

download-models: ## Download GenConViT model weights
	@echo "📥 Downloading GenConViT model weights..."
	cd services/inference-worker && python download_models.py

build: ## Build all services
	@echo "🏗️ Building Project Sentinel services..."
	@echo "Building AI inference service..."
	cd services/inference-worker && docker build -t sentinel-inference .
	@echo "Building browser extension..."
	cd apps/extension && npm run build
	@echo "✅ Build complete!"

dev: ## Start development environment
	@echo "🚀 Starting Project Sentinel development environment..."
	docker-compose up -d
	@echo "✅ Services started!"
	@echo "   - AI Inference: http://localhost:8000"
	@echo "   - Backend API: http://localhost:3000"
	@echo "   - Redis Cache: localhost:6379"

test: ## Run system integration tests
	@echo "🧪 Running Project Sentinel integration tests..."
	python test_system.py

test-inference: ## Test AI inference service only
	@echo "🧪 Testing AI inference service..."
	curl -X GET http://localhost:8000/health

test-api: ## Test backend API only
	@echo "🧪 Testing backend API..."
	curl -X GET http://localhost:3000/health

deploy-replicate: ## Deploy AI service to Replicate
	@echo "🚀 Deploying to Replicate..."
	./deploy/replicate-deploy.sh

deploy-vercel: ## Deploy API to Vercel
	@echo "🚀 Deploying to Vercel..."
	./deploy/vercel-deploy.sh

deploy: deploy-replicate deploy-vercel ## Deploy all services to production

logs: ## Show service logs
	docker-compose logs -f

stop: ## Stop development environment
	@echo "🛑 Stopping Project Sentinel services..."
	docker-compose down

clean: ## Clean up containers and volumes
	@echo "🧹 Cleaning up Project Sentinel..."
	docker-compose down -v
	docker system prune -f
	@echo "✅ Cleanup complete!"

extension-dev: ## Build extension for development
	@echo "🔧 Building extension for development..."
	cd apps/extension && npm run build
	@echo "✅ Extension built! Load dist/ folder in Chrome Developer Mode"

extension-prod: ## Build extension for production
	@echo "📦 Building extension for production..."
	cd apps/extension && npm run build
	cd apps/extension && zip -r sentinel-extension.zip dist/
	@echo "✅ Extension package created: apps/extension/sentinel-extension.zip"

demo: ## Prepare for hackathon demo
	@echo "🎯 Preparing Project Sentinel for hackathon demo..."
	make build
	make test
	make extension-prod
	@echo "🏆 Demo ready! All systems operational!"

status: ## Check system status
	@echo "📊 Project Sentinel System Status"
	@echo "================================="
	@echo "AI Inference Service:"
	@curl -s http://localhost:8000/health | jq . || echo "❌ Not running"
	@echo "\nBackend API:"
	@curl -s http://localhost:3000/health | jq . || echo "❌ Not running"
	@echo "\nDocker Services:"
	@docker-compose ps

# Default target
.DEFAULT_GOAL := help
