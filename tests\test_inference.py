"""
Project Sentinel - AI Inference Service Tests
"""

import pytest
import asyncio
import tempfile
import os
from unittest.mock import Mock, patch

import torch
import numpy as np

# Import our inference module
import sys
sys.path.append('services/inference-worker')
from genconvit_inference import GenConViTInference

class TestGenConViTInference:
    """Test suite for GenConViT inference service"""
    
    @pytest.fixture
    async def inference_service(self):
        """Create inference service instance"""
        service = GenConViTInference()
        # Mock model loading for testing
        service.model = Mock()
        service.device = torch.device('cpu')
        service.config = {
            "model": {"latent_dims": 12544, "backbone": "convnext_tiny"},
            "num_classes": 2
        }
        return service
    
    def test_init(self):
        """Test inference service initialization"""
        service = GenConViTInference()
        assert service.device is not None
        assert isinstance(service.fp16, bool)
    
    @pytest.mark.asyncio
    async def test_load_config(self, inference_service):
        """Test configuration loading"""
        config = inference_service._load_config()
        assert "model" in config
        assert "backbone" in config["model"]
        assert "embedder" in config["model"]
        assert config["num_classes"] == 2
    
    @pytest.mark.asyncio
    async def test_extract_frames(self, inference_service):
        """Test frame extraction from video"""
        # Create a dummy video file for testing
        with tempfile.NamedTemporaryFile(suffix='.mp4', delete=False) as tmp_file:
            # This would need a real video file for full testing
            # For now, test the error handling
            try:
                frames = inference_service._extract_frames(tmp_file.name, 10)
                # Should handle empty/invalid video gracefully
            except Exception as e:
                assert "Video contains no frames" in str(e) or "No such file" in str(e)
            finally:
                os.unlink(tmp_file.name)
    
    @pytest.mark.asyncio
    async def test_extract_faces(self, inference_service):
        """Test face extraction from frames"""
        # Create dummy frames (random noise)
        dummy_frames = np.random.randint(0, 255, (5, 224, 224, 3), dtype=np.uint8)
        
        faces, count = inference_service._extract_faces(dummy_frames)
        
        # Should return valid arrays even if no faces detected
        assert isinstance(faces, np.ndarray)
        assert isinstance(count, int)
        assert count >= 0
    
    @pytest.mark.asyncio
    async def test_preprocess_faces(self, inference_service):
        """Test face preprocessing"""
        # Create dummy face images
        dummy_faces = np.random.randint(0, 255, (3, 224, 224, 3), dtype=np.uint8)
        
        tensor = inference_service._preprocess_faces(dummy_faces)
        
        # Check tensor properties
        assert isinstance(tensor, torch.Tensor)
        assert tensor.shape == (3, 3, 224, 224)  # NCHW format
        assert tensor.device == inference_service.device
    
    @pytest.mark.asyncio
    async def test_predict_video_no_faces(self, inference_service):
        """Test prediction when no faces are detected"""
        # Mock the face extraction to return empty tensor
        with patch.object(inference_service, '_extract_and_preprocess_faces') as mock_extract:
            mock_extract.return_value = torch.tensor([])
            
            result = await inference_service.predict_video("dummy_path.mp4", 15)
            
            assert result["deepfake_probability"] == 0.5
            assert result["confidence_score"] == 0.0
            assert "No faces detected" in result["error"]
    
    @pytest.mark.asyncio
    async def test_predict_video_success(self, inference_service):
        """Test successful video prediction"""
        # Mock successful face extraction and model prediction
        dummy_tensor = torch.randn(5, 3, 224, 224)
        mock_prediction = torch.tensor([[0.3, 0.7], [0.2, 0.8], [0.4, 0.6]])
        
        with patch.object(inference_service, '_extract_and_preprocess_faces') as mock_extract:
            mock_extract.return_value = dummy_tensor
            
            with patch.object(inference_service.model, 'forward') as mock_forward:
                mock_forward.return_value = mock_prediction
                
                result = await inference_service.predict_video("dummy_path.mp4", 15)
                
                assert "deepfake_probability" in result
                assert "confidence_score" in result
                assert "processing_time" in result
                assert result["frames_analyzed"] == 5

class TestModelArchitecture:
    """Test GenConViT model architecture"""
    
    def test_model_config(self):
        """Test model configuration"""
        config = {
            "model": {
                "backbone": "convnext_tiny",
                "embedder": "swin_tiny_patch4_window7_224",
                "latent_dims": 12544
            },
            "num_classes": 2,
            "img_size": 224
        }
        
        assert config["model"]["backbone"] == "convnext_tiny"
        assert config["model"]["embedder"] == "swin_tiny_patch4_window7_224"
        assert config["num_classes"] == 2
    
    def test_tensor_shapes(self):
        """Test tensor shape transformations"""
        # Test input tensor
        input_tensor = torch.randn(4, 3, 224, 224)  # Batch of 4 images
        assert input_tensor.shape == (4, 3, 224, 224)
        
        # Test normalization
        normalized = (input_tensor - 0.5) / 0.5
        assert normalized.shape == input_tensor.shape
        assert torch.allclose(normalized.mean(), torch.tensor(0.0), atol=0.1)

if __name__ == "__main__":
    pytest.main([__file__, "-v"])
