"""
Project Sentinel - Backend API Orchestrator
FastAPI service for managing deepfake detection jobs
"""

import os
import uuid
import time
import json
import logging
from typing import Optional, Dict, Any

import httpx
from fastapi import FastAPI, HTTPException, Request
from fastapi.middleware.cors import CORSMiddleware
from pydantic import BaseModel, HttpUrl

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Environment configuration
INFERENCE_SERVICE_URL = os.getenv("INFERENCE_SERVICE_URL", "http://localhost:8000")
KV_REST_API_URL = os.getenv("KV_REST_API_URL")
KV_REST_API_TOKEN = os.getenv("KV_REST_API_TOKEN")

# In-memory cache for development (replace with Vercel KV in production)
job_cache: Dict[str, Dict[str, Any]] = {}

app = FastAPI(
    title="Project Sentinel - API Orchestrator",
    description="Backend API for coordinating deepfake detection",
    version="1.0.0"
)

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

class AnalysisRequest(BaseModel):
    video_url: HttpUrl
    num_frames: int = 15

class AnalysisResponse(BaseModel):
    job_id: str
    status: str
    message: str

class StatusResponse(BaseModel):
    job_id: str
    status: str
    deepfake_probability: Optional[float] = None
    confidence_score: Optional[float] = None
    processing_time: Optional[float] = None
    frames_analyzed: Optional[int] = None
    device_used: Optional[str] = None
    error: Optional[str] = None
    created_at: Optional[float] = None
    completed_at: Optional[float] = None

class WebhookPayload(BaseModel):
    job_id: str
    status: str
    result: Dict[str, Any]
    timestamp: float

@app.get("/")
async def root():
    """Root endpoint"""
    return {
        "service": "Project Sentinel API Orchestrator",
        "version": "1.0.0",
        "status": "operational",
        "inference_service": INFERENCE_SERVICE_URL
    }

@app.get("/health")
async def health_check():
    """Health check endpoint"""
    # Check inference service health
    try:
        async with httpx.AsyncClient(timeout=5.0) as client:
            response = await client.get(f"{INFERENCE_SERVICE_URL}/health")
            inference_healthy = response.status_code == 200
    except:
        inference_healthy = False
    
    return {
        "status": "healthy",
        "inference_service_healthy": inference_healthy,
        "cache_size": len(job_cache),
        "timestamp": time.time()
    }

@app.post("/api/analyze", response_model=AnalysisResponse)
async def analyze_video(request: AnalysisRequest):
    """
    Submit video for deepfake analysis
    Returns job_id for status polling
    """
    try:
        # Generate unique job ID
        job_id = str(uuid.uuid4())
        
        # Store job in cache
        job_data = {
            "job_id": job_id,
            "video_url": str(request.video_url),
            "num_frames": request.num_frames,
            "status": "submitted",
            "created_at": time.time(),
            "completed_at": None,
            "result": None,
            "error": None
        }
        
        await store_job(job_id, job_data)
        
        # Call inference service asynchronously
        callback_url = f"{request.url.scheme}://{request.url.netloc}/api/webhook"
        
        async with httpx.AsyncClient(timeout=5.0) as client:
            inference_request = {
                "video_url": str(request.video_url),
                "callback_url": callback_url,
                "job_id": job_id,
                "num_frames": request.num_frames
            }
            
            response = await client.post(
                f"{INFERENCE_SERVICE_URL}/predict",
                json=inference_request
            )
            
            if response.status_code != 200:
                raise HTTPException(status_code=502, detail="Inference service unavailable")
        
        logger.info(f"🎬 Submitted job {job_id} for analysis")
        
        return AnalysisResponse(
            job_id=job_id,
            status="processing",
            message="Video submitted for analysis"
        )
        
    except Exception as e:
        logger.error(f"❌ Failed to submit analysis: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.post("/api/webhook")
async def webhook_handler(payload: WebhookPayload):
    """Handle inference service callbacks"""
    try:
        logger.info(f"📥 Received webhook for job {payload.job_id}")

        # Get existing job data
        job_data = await get_job(payload.job_id)
        if not job_data:
            raise HTTPException(status_code=404, detail="Job not found")

        # Update job with results
        job_data["status"] = payload.status
        job_data["completed_at"] = payload.timestamp

        if payload.status == "completed":
            job_data["result"] = payload.result
            logger.info(f"✅ Job {payload.job_id} completed successfully")
        else:
            job_data["error"] = payload.result.get("error", "Unknown error")
            logger.error(f"❌ Job {payload.job_id} failed: {job_data['error']}")

        await store_job(payload.job_id, job_data)

        return {"status": "received"}

    except Exception as e:
        logger.error(f"❌ Webhook processing failed: {e}")
        raise HTTPException(status_code=500, detail=str(e))

async def store_job(job_id: str, job_data: Dict[str, Any]):
    """Store job data in cache/KV store"""
    if KV_REST_API_URL and KV_REST_API_TOKEN:
        # Use Vercel KV in production
        try:
            async with httpx.AsyncClient() as client:
                await client.post(
                    f"{KV_REST_API_URL}/set/{job_id}",
                    headers={"Authorization": f"Bearer {KV_REST_API_TOKEN}"},
                    json=job_data
                )
        except Exception as e:
            logger.error(f"Failed to store in KV: {e}")
            # Fallback to memory cache
            job_cache[job_id] = job_data
    else:
        # Use in-memory cache for development
        job_cache[job_id] = job_data

async def get_job(job_id: str) -> Optional[Dict[str, Any]]:
    """Retrieve job data from cache/KV store"""
    if KV_REST_API_URL and KV_REST_API_TOKEN:
        # Use Vercel KV in production
        try:
            async with httpx.AsyncClient() as client:
                response = await client.get(
                    f"{KV_REST_API_URL}/get/{job_id}",
                    headers={"Authorization": f"Bearer {KV_REST_API_TOKEN}"}
                )
                if response.status_code == 200:
                    return response.json()
        except Exception as e:
            logger.error(f"Failed to get from KV: {e}")

    # Fallback to memory cache
    return job_cache.get(job_id)

# For Vercel deployment
handler = app

@app.get("/api/status/{job_id}", response_model=StatusResponse)
async def get_job_status(job_id: str):
    """Get analysis job status and results"""
    try:
        job_data = await get_job(job_id)
        
        if not job_data:
            raise HTTPException(status_code=404, detail="Job not found")
        
        response = StatusResponse(
            job_id=job_id,
            status=job_data["status"],
            created_at=job_data["created_at"],
            completed_at=job_data["completed_at"]
        )
        
        # Add result data if completed
        if job_data["result"]:
            result = job_data["result"]
            response.deepfake_probability = result.get("deepfake_probability")
            response.confidence_score = result.get("confidence_score")
            response.processing_time = result.get("processing_time")
            response.frames_analyzed = result.get("frames_analyzed")
            response.device_used = result.get("device_used")
        
        if job_data["error"]:
            response.error = job_data["error"]
        
        return response
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"❌ Failed to get job status: {e}")
        raise HTTPException(status_code=500, detail=str(e))
